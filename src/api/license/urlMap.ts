const enum UrlMap {
  GET_ADD_LICENSE_FORM_OPTIONS = '/license/get/user-from',
  GET_ADD_LICENSE_EXPIRE_DATE = '/license/get/expiration-date',
  POST_ADD_LICENSE = '/license/user/create-license',
  POST_ADD_POOL_LICENSE = '/license/license-pool/user/create-license',
  GET_LICENSE_LIST = '/license/list',
  PUT_LICENSE_KEY = '/license/refresh/license-key',
  PUT_LICENSE_NAME = '/license/update/name',
  GET_ADD_POOL_LICENSE_FORM_OPTIONS = '/license/license-pool/get/user-from',
  GET_TOKEN_LIST = '/license/license-token/list',
  DEL_TOKEN = '/license/license-token/delete',
  GET_SUMMARY_LIST = '/license/license-summary/list',
  POST_ADD_TOKEN = '/license/license-token/add',
  POST_PASSWORD_ENCRYPT = '/license/password-encryption/create',
  GET_LICENSE_COUNT = '/license/get/free-license-count',
  GET_POOL_LICENSE_LIST = '/license/license-pool/list',
  POST_LICENSE_EXCEL_UPLOAD = '/license/license-excel/import',
  GET_LICENSE_EXCEL_TEMPLATE = '/license/license-excel/export-template',
  POST_LICENSE_EXCEL_DOWNLOAD = '/license/license-excel/export',
  GET_SPEED_TYPE = '/license/license-excel/get-speed-type',
}
export default UrlMap;

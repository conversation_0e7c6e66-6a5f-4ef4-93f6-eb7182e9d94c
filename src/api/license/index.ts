import {
  AddLicenseFieldType,
  CompanyCountType,
  DownloadExcelParams,
  LicenseFormOptionData,
} from '../interface';
import request from '../request';
import UrlMap from './urlMap';

const getAddLicenseFormOptions = () =>
  request<LicenseFormOptionData>({
    url: UrlMap.GET_ADD_LICENSE_FORM_OPTIONS,
    method: 'get',
  });

const getAddLicenseExpireDate = (params: {
  featureTypeId: number;
  mode: string;
  speedTypeId: number;
}) =>
  request<LicenseFormOptionData>({
    url: UrlMap.GET_ADD_LICENSE_EXPIRE_DATE,
    method: 'get',
    params,
  });

const postAddLicense = (data: AddLicenseFieldType) => {
  return request({
    url: UrlMap.POST_ADD_LICENSE,
    method: 'post',
    data,
  });
};
const postAddPoolLicense = (data: AddLicenseFieldType) => {
  return request({
    url: UrlMap.POST_ADD_POOL_LICENSE,
    method: 'post',
    data,
  });
};
const getLicenseList = () =>
  request({ url: UrlMap.GET_LICENSE_LIST, method: 'post' });

const putLicenseKey = (id: number) =>
  request({ url: UrlMap.PUT_LICENSE_KEY + '/' + id, method: 'put' });

const putLicenseName = (data: { id: number; name: string }) =>
  request({ url: UrlMap.PUT_LICENSE_NAME, method: 'put', data });

const getAddPoolFormOptions = () =>
  request<LicenseFormOptionData>({
    url: UrlMap.GET_ADD_POOL_LICENSE_FORM_OPTIONS,
    method: 'get',
  });

const getTokenList = () =>
  request({ url: UrlMap.GET_TOKEN_LIST, method: 'post' });

const delToken = (id: number) =>
  request({ url: UrlMap.DEL_TOKEN + '/' + id, method: 'post' });

const getSummaryList = () =>
  request({ url: UrlMap.GET_SUMMARY_LIST, method: 'get' });

const postAddToken = (data: { comments: string }) =>
  request({
    url: UrlMap.POST_ADD_TOKEN,
    method: 'post',
    data,
  });

const postPasswordEncrypt = (data: { password: string }) =>
  request({
    url: UrlMap.POST_PASSWORD_ENCRYPT,
    method: 'post',
    data,
  });

const getLicenseCount = () =>
  request<CompanyCountType>({ url: UrlMap.GET_LICENSE_COUNT, method: 'get' });

const getPoolLicenseList = () =>
  request({ url: UrlMap.GET_POOL_LICENSE_LIST, method: 'get' });

const downloadExcelTemplate = () =>
  request({
    url: UrlMap.GET_LICENSE_EXCEL_TEMPLATE,
    method: 'get',
    responseType: 'blob',
  });

const downloadLicenseExcel = (params: DownloadExcelParams) =>
  request({
    url: UrlMap.POST_LICENSE_EXCEL_DOWNLOAD,
    method: 'post',
    responseType: 'blob',
    data: params,
  });

const uploadLicenseExcel = (data: FormData) =>
  request({ url: UrlMap.POST_LICENSE_EXCEL_UPLOAD, method: 'post', data });

const getSpeedType = () =>
  request({ url: UrlMap.GET_SPEED_TYPE, method: 'get' });
export {
  getAddLicenseFormOptions,
  getAddLicenseExpireDate,
  postAddLicense,
  postAddPoolLicense,
  getLicenseList,
  putLicenseKey,
  putLicenseName,
  getAddPoolFormOptions,
  getTokenList,
  delToken,
  getSummaryList,
  postAddToken,
  postPasswordEncrypt,
  getLicenseCount,
  getPoolLicenseList,
  downloadExcelTemplate,
  downloadLicenseExcel,
  uploadLicenseExcel,
  getSpeedType,
};

import { noSearchPagination } from '../interface';
import request from '../request';
import UrlMap from './urlMap';

type AuthDetailListParams = {
  current: number;
  size: number;
  companyId?: string;
};

export const getCompanyAmpconList = (params: any) => {
  return request({
    url: UrlMap.POST_COMPANY_AMPCON_LIST,
    method: 'POST',
    data: params,
  });
};

export const postAddCompanyAmpconForm = (params: any) => {
  return request({
    url: UrlMap.POST_ADD_COMPANY_AMPCON,
    method: 'POST',
    data: params,
  });
};

export const getCompanyAmpconDetail = (id: string) => {
  return request({
    url: UrlMap.GET_COMPANY_AMPCON_DETAIL,
    method: 'GET',
    params: { id },
  });
};

export const postAuthDetailList = (data: AuthDetailListParams) => {
  return request({
    url: UrlMap.POST_AUTH_DETAIL_LIST,
    method: 'POST',
    data,
  });
};

export const postAuthLogsList = (params: any) => {
  return request({
    url: UrlMap.POST_AUTH_LOGS_LIST,
    method: 'POST',
    data: params,
  });
};

export const getLogsFilter = () => {
  return request({
    url: UrlMap.GET_LOGS_FILTER,
    method: 'GET',
  });
};

export const getAmpconLicenseSummaryDetail = (id: string) => {
  return request({
    url: UrlMap.GET_AUTH_DETAIL_FORM_DATA,
    method: 'GET',
    params: { id },
  });
};

export const putAmpconSummary = (data: any) => {
  return request({
    url: UrlMap.PUT_AMPCON_LICENSE_SUMMARY,
    method: 'PUT',
    data,
  });
};

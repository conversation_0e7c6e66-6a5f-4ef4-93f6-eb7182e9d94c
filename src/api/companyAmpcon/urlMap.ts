const enum UrlMap {
  POST_COMPANY_AMPCON_LIST = '/license/admin/amp-con/auth-mgr/list-company',
  GET_COMPANY_AMPCON_DETAIL = '/license/admin/amp-con/auth-mgr',
  POST_ADD_COMPANY_AMPCON = '/license/admin/amp-con/auth-mgr',
  POST_AUTH_DETAIL_LIST = '/license/admin/amp-con/auth-mgr/list-detail',
  POST_AUTH_LOGS_LIST = '/license/admin/amp-con/auth-mgr/logs',
  GET_LOGS_FILTER = '/license/admin/amp-con/auth-mgr/logs-filter-items',
  GET_AUTH_DETAIL_FORM_DATA = '/license/admin/amp-con/auth-mgr/get-edit-from',
  PUT_AMPCON_LICENSE_SUMMARY = '/license/admin/amp-con/auth-mgr/update',
}

export default UrlMap;

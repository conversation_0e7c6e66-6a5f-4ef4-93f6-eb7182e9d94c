const enum urlMap {
  GET_PARAMETER_CONFIG = '/license/admin/amp-con/parameter-config/list',
  DEL_PARAMETER_CONFIG = '/license/admin/amp-con/parameter-config/batch-delete',
  GET_PARAMETER_CONFIG_LIST_FILTER = '/license/admin/amp-con/parameter-config/list-filter-items',
  GET_PARAMETER_CONFIG_DETAIL_OPTIONS = '/license/admin/amp-con/parameter-config/add-form',
  POST_PARAMETER_CONFIG_DETAIL_ADD_FORM = '/license/admin/amp-con/parameter-config/add-form',
  GET_PARAMETER_CONFIG_DETAIL_BY_ID = '/license/admin/amp-con/parameter-config/edit-form',
  PUT_PARAMETER_CONFIG_DETAIL_BY_ID = '/license/admin/amp-con/parameter-config/edit-form',
  GET_AMPCON_LICENSE_LIST = '/license/admin/amp-con/license/list',
  GET_AMPCON_LICENSE_LIST_FILTER = '/license/admin/amp-con/license/list-filter-items',
  GET_HW_ID_LIST = '/license/admin/amp-con/license/get-license-detail',
  DEL_AMPCON_LICENSE = '/license/admin/amp-con/license/batch-delete',
  GET_AMPCON_LICENSE_DETAIL_FILTER = '/license/admin/amp-con/license/add-form',
  GET_AMPCON_LICENSE_EXPIRATION_DATE = '/license/admin/amp-con/license/get-expiration-date',
  POST_SAVE_AMPCON_LICENSE = '/license/admin/amp-con/license/add-form',
  GET_AMPCON_LICENSE_DETAIL_BY_ID = '/license/admin/amp-con/license/edit-form',
  GET_DOWNLOAD_AMPCON_LICENSE_TEMPLATE = '/license/admin/amp-con/license/export/template',
  GET_DOWNLOAD_TEMPLATE_BY_ID = '/license/admin/amp-con/license/export/hardware-ids',
  POST_USER_AMPCON_LICENSE_LIST = '/license/user/amp-con/list',
  POST_USER_AMPCON_LICENSE_ADD = '/license/user/amp-con/save',
  POST_UPDATE_AMPCON_LICENSE_NAME = '/license/user/amp-con/update-license-name',
  GET_USER_AMPCON_LICENSE_LIST_FILTER = '/license/user/amp-con/list-filter-items',
  GET_USER_AMPCON_LICENSE_DETAIL_FILTER = '/license/user/amp-con/add-form',
  GET_USER_AMPCON_LICENSE_EXPIRATION_DATE = '/license/user/amp-con/get-expiration-date',
  GET_USER_AMPCON_LICENSE_TEMPLATE = '/license/user/amp-con/export/template',
  POST_SAVE_REVOKE_CODE = '/license/user/amp-con/save-revoke-code',
  GET_USER_HW_ID_LIST = '/license/user/amp-con/get-license-detail',
  GET_USER_REVOKE_TEMPLATE = '/license/user/amp-con/export/revoke-template',
  GET_EXPIRATION_DATE_BY_ID = '/license/user/amp-con/get-expiration-date-id',
  POST_ADD_DETAIL = '/license/user/amp-con/add-detail',
  POST_AMPCON_SUMMARY_LIST = '/license/user/amp-con/list-summary',
  GET_DELETE_DETAIL = '/license/admin/amp-con/parameter-config/select-delete-details',
  GET_AMPCON_LOGS = '/license/admin/amp-con/logs/list',
  GET_AMPCON_LOGS_FILTER = '/license/admin/amp-con/logs/list-filter-items',
  GET_AMPCON_LOGS_DETAIL = '/license/admin/amp-con/logs/get',
  GET_AMPCON_REPORT_FILTER = '/license/admin/amp-con/reports/get-init',
  POST_AMPCON_REPORT_EXCEL_DOWNLOAD = '/license/admin/amp-con/reports/export',
  GET_REVOKE_CODE_FILTER = '/license/user/amp-con/get-revoke-code-form',
  POST_AMPCON_EXCEL_ANALYSIS = '/license/user/amp-con/import/analysis-excel-hardware-ids',
}

export default urlMap;

import { Key } from 'react';
import { ParameterListParams } from '../interface';
import request from '../request';
import urlMap from './urlMap';

interface ParameterAddParams {
  code: string;
  name: string;
  parameter: number;
  parameterValue: string;
  softwareType: number;
  softwareTypeValue: string;
}

export interface LicensesPageParams {
  companyId?: number;
  createTime?: any[];
  current: number;
  deviceType?: number;
  expireTime?: any[];
  featureType?: number;
  hardwareId?: string;
  keyword?: string;
  size: number;
  softwareType?: number;
  softwareVersion?: number;
}

export interface AmpconExpirationDateParams {
  companyId: number;
  deviceType: number;
  featureType: number;
  licenseType: number;
  softwareType: number;
  softwareVersion: number;
}

export const getParameterConfigList = (params: ParameterListParams) => {
  return request({
    url: urlMap.GET_PARAMETER_CONFIG,
    method: 'post',
    data: params,
  });
};
export const delParameterConfig = (params: { ids: Key[] }) => {
  return request({
    url: urlMap.DEL_PARAMETER_CONFIG,
    method: 'delete',
    data: params,
  });
};
export const getParameterConfigListFilter = () => {
  return request({
    url: urlMap.GET_PARAMETER_CONFIG_LIST_FILTER,
    method: 'get',
  });
};

export const getParameterConfigDetailOptions = () => {
  return request({
    url: urlMap.GET_PARAMETER_CONFIG_DETAIL_OPTIONS,
    method: 'get',
  });
};

export const postParameterConfigAddForm = (data: ParameterAddParams) => {
  return request({
    url: urlMap.POST_PARAMETER_CONFIG_DETAIL_ADD_FORM,
    method: 'post',
    data,
  });
};

export const getParameterDetailById = (id: string) => {
  return request({
    url: urlMap.GET_PARAMETER_CONFIG_DETAIL_BY_ID + '/' + id,
    method: 'get',
  });
};
export const putParameterConfigDetail = (data: any) => {
  return request({
    url: urlMap.PUT_PARAMETER_CONFIG_DETAIL_BY_ID,
    method: 'put',
    data,
  });
};
export const getAmpconLicensesList = (data: LicensesPageParams) => {
  return request({
    url: urlMap.GET_AMPCON_LICENSE_LIST,
    method: 'post',
    data,
  });
};

export const getAmpconLicensesListFilter = () => {
  return request({
    url: urlMap.GET_AMPCON_LICENSE_LIST_FILTER,
    method: 'get',
  });
};

export const getHwIdsById = (id: number) => {
  return request({
    url: urlMap.GET_HW_ID_LIST + '/' + id,
    method: 'get',
  });
};

export const delAmpconLicense = (data: { ids: Key[] }) => {
  return request({
    url: urlMap.DEL_AMPCON_LICENSE,
    method: 'delete',
    data,
  });
};

export const getAmpconLicenseDetailOptions = () => {
  return request({
    url: urlMap.GET_AMPCON_LICENSE_DETAIL_FILTER,
    method: 'get',
  });
};

export const getAmpconLicensesExpirationDate = (
  data: AmpconExpirationDateParams
) => {
  return request({
    url: urlMap.GET_AMPCON_LICENSE_EXPIRATION_DATE,
    method: 'post',
    data,
  });
};

export const saveAmpconLicenses = (data: any) => {
  return request({
    url: urlMap.POST_SAVE_AMPCON_LICENSE,
    method: 'post',
    data,
  });
};
export const updateAmpconLicenses = (data: any) => {
  return request({
    url: urlMap.GET_AMPCON_LICENSE_DETAIL_BY_ID,
    method: 'post',
    data,
  });
};

export const getAmpconLicenseDetail = (id: string) => {
  return request({
    url: urlMap.GET_AMPCON_LICENSE_DETAIL_BY_ID + '/' + id,
    method: 'get',
  });
};

export const downloadAmpconTemplate = () => {
  return request({
    url: urlMap.GET_DOWNLOAD_AMPCON_LICENSE_TEMPLATE,
    method: 'get',
    responseType: 'blob',
  });
};

export const downloadAmpconTemplateById = (id: string) => {
  return request({
    url: urlMap.GET_DOWNLOAD_TEMPLATE_BY_ID + '/' + id,
    method: 'get',
    responseType: 'blob',
  });
};

export const postUserAmpconLicensesList = (data: LicensesPageParams) => {
  return request({
    url: urlMap.POST_USER_AMPCON_LICENSE_LIST,
    method: 'post',
    data,
  });
};

export const saveUserAmpconLicenses = (data: any) => {
  return request({
    url: urlMap.POST_USER_AMPCON_LICENSE_ADD,
    method: 'post',
    data,
  });
};

export const updateAmpconLicenseName = (data: any) => {
  return request({
    url: urlMap.POST_UPDATE_AMPCON_LICENSE_NAME,
    method: 'post',
    data,
  });
};

export const getUserAmpconLicensesListFilter = () => {
  return request({
    url: urlMap.GET_USER_AMPCON_LICENSE_LIST_FILTER,
    method: 'get',
  });
};
export const getUserAmpconLicenseDetailOptions = () => {
  return request({
    url: urlMap.GET_AMPCON_LICENSE_DETAIL_FILTER,
    method: 'get',
  });
};

export const getUserAmpconLicensesExpirationDate = (
  data: AmpconExpirationDateParams
) => {
  return request({
    url: urlMap.GET_USER_AMPCON_LICENSE_EXPIRATION_DATE,
    method: 'post',
    data,
  });
};

export const downloadUserAmpconTemplate = () => {
  return request({
    url: urlMap.GET_USER_AMPCON_LICENSE_TEMPLATE,
    method: 'get',
    responseType: 'blob',
  });
};

export const saveRevokeCode = (data: any) => {
  return request({
    url: urlMap.POST_SAVE_REVOKE_CODE,
    method: 'post',
    data,
  });
};

export const getUserHwIdsById = (id: number) => {
  return request({
    url: urlMap.GET_USER_HW_ID_LIST + '/' + id,
    method: 'get',
  });
};

export const downloadRevokeTemplate = () => {
  return request({
    url: urlMap.GET_USER_REVOKE_TEMPLATE,
    method: 'get',
    responseType: 'blob',
  });
};

export const getExpirationDateById = (id: number) => {
  return request({
    url: urlMap.GET_EXPIRATION_DATE_BY_ID,
    method: 'get',
    params: { id },
  });
};

export const addHwIds = (data: any) => {
  return request({
    url: urlMap.POST_ADD_DETAIL,
    method: 'post',
    data,
  });
};

export const postUserSummaryList = (data: any) => {
  return request({
    url: urlMap.POST_AMPCON_SUMMARY_LIST,
    method: 'post',
    data,
  });
};
export const getSelectDeleteDetail = (data: { ids: Key[] }) => {
  return request({
    url: urlMap.GET_DELETE_DETAIL,
    method: 'post',
    data,
  });
};

export const getAmpconLogsList = (data: any) => {
  return request({
    url: urlMap.GET_AMPCON_LOGS,
    method: 'post',
    data,
  });
};

export const getAmpconLogsListFilter = () => {
  return request({
    url: urlMap.GET_AMPCON_LOGS_FILTER,
    method: 'get',
  });
};

export const getAmpconLogsDetail = (id: number) => {
  return request({
    url: urlMap.GET_AMPCON_LOGS_DETAIL + '/' + id,
    method: 'get',
  });
};

export const postAmpconReportExcelDownload = (data: any) => {
  return request({
    url: urlMap.POST_AMPCON_REPORT_EXCEL_DOWNLOAD,
    method: 'post',
    data,
    responseType: 'blob',
  });
};

export const getAmpconReportFilter = () => {
  return request({
    url: urlMap.GET_AMPCON_REPORT_FILTER,
    method: 'get',
  });
};

export const getRevokeCodeFilterItems = () => {
  return request({
    url: urlMap.GET_REVOKE_CODE_FILTER,
    method: 'get',
  });
};

// 解析 Excel 中的设备Id 返回给前端
export const analysisExcel = (data: any) => {
  return request({
    url: urlMap.POST_AMPCON_EXCEL_ANALYSIS,
    method: 'post',
    data,
  });
};

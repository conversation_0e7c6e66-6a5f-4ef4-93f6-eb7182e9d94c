const enum urlMap {
  'GET_LICENSE_DELETE_LOGS' = '/license/admin/license-delete/logs/list',
  'GET_LICENSE_DELETE_LOGS_DETAIL' = '/license/admin/license-delete/logs/get',
  'GET_LICENSE_EDIT_LOGS' = '/license/admin/license-edit/logs/list',
  'GET_LICENSE_EDIT_LOGS_DETAIL' = '/license/admin/license-edit/logs/get',
  'GET_LICENSE_LIMITATION_LOGS' = '/license/admin/license-limitation/logs/list',
  'GET_LICENSE_LIMITATION_LOGS_DETAIL' = '/license/admin/license-limitation/logs/get',
  'GET_LICENSE_REPORTING' = '/license/admin/license-report/list',
  'GET_LICENSE_REPORTING_DETAIL' = '/license/admin/license-report/get',
  'GET_LICENSE_SUMMARY_DETAIL_LIST' = '/license/admin/license-summary/list',
  'GET_LICENSE_SUMMARY_DETAIL_FORM' = '/license/admin/license-summary/get-edit-from',
  'PUT_LICENSE_SUMMARY_DETAIL_FORM' = '/license/admin/license-summary/update',
  'GET_LICENSE_SUMMARY_LOGS' = '/license/admin/license-summary/logs/list',
  'GET_LICENSE_SUMMARY_LOGS_DETAIL' = '/license/admin/license-summary/logs/get',
  'GET_LICENSE_BATCH_EXTEND_LOGS' = '/license/batch-extend/logs/list',
  'GET_LICENSE_BATCH_EXTEND_LOGS_DETAIL' = '/license/batch-extend/logs/get',
  'GET_LICENSE_BATCH_EXTEND' = '/license/batch-extend/list',
  'GET_LICENSE_BATCH_EXTEND_DETAIL' = '/license/batch-extend/get/edit-from',
  'GET_LICENSE_BATCH_EXTEND_FORM' = '/license/batch-extend/get/add-from',
  'POST_LICENSE_BATCH_EXTEND' = '/license/batch-extend/create',
  POST_LICENSE_BATCH_EXTEND_UPDATE = '/license/batch-extend/update',
  'GET_FEATURE_TYPES_LIST' = '/license/feature-type/list',
  'DEL_FEATURE_TYPES' = '/license/feature-type/batch-delete',
  'POST_FEATURE_TYPES' = '/license/feature-type/create',
  'GET_FEATURE_TYPES_BY_ID' = '/license/feature-type/get/edit-from',
  'PUT_FEATURE_TYPES' = '/license/feature-type/update',
  GET_EXPIRATION_DATE = '/license/admin/license/get/expiration-date',
  GET_ADMIN_LICENSE_LIST = '/license/admin/license/list',
  DEL_ADMIN_LICENSE = '/license/admin/license/batch-delete',
  PUT_ADMIN_LICENSE_DISABLE = '/license/admin/license/batch-disable',
  PUT_ADMIN_LICENSE_ENABLE = '/license/admin/license/batch-enable',
  GET_ADMIN_LICENSE_ADD_FORM = '/license/admin/license/get/from',
  GET_ADMIN_LICENSE_DETAIL_BY_ID = '/license/admin/license/get/edit-from',
  POST_ADMIN_LICENSE = '/license/admin/license/create-license',
  PUT_ADMIN_LICENSE = '/license/admin/license/update-license',
  PUT_ADMIN_LICENSE_KEY = '/license/admin/license/refresh/license-key',
  GET_ADMIN_POOL_LICENSE_LIST = '/license/admin/license-pool/list',
  DEL_ADMIN_POOL_LICENSE = '/license/admin/license-pool/batch-delete',
  PUT_ADMIN_POOL_LICENSE_RENEWED = '/license/admin/license-pool/batch-renew-license',
  GET_ADMIN_POOL_LICENSE_ADD_FORM = '/license/admin/license-pool/get-add-from',
  GET_ADMIN_POOL_LICENSE_DETAIL_BY_ID = '/license/admin/license-pool/get-edit/form',
  POST_ADMIN_POOL_LICENSE = '/license/admin/license-pool/create-license-pool',
  PUT_ADMIN_POOL_LICENSE = '/license/admin/license-pool/update-license-pool',
  PUT_ADMIN_POOL_LICENSE_KEY = '/license/admin/license-pool/refresh/license-key',
  GET_POOL_LICENSE_EXPIRATION_DATE = '/license/admin/license-pool/get/expiration-date',
  GET_FEATURE_TYPES_RELATION_LIST = '/license/feature-type/batch-delete-select',
  GET_LICENSE_REPORTING_DOWNLOAD = '/license/admin/license-report/download',
  GET_HISTORY_LIST = '/license/admin/log/list',
  GET_COMPANY_LIST = '/license/company/list-filter',
  POST_COMPANY_EXCEL_UPLOAD = '/license/admin/excel/import-change',
  POST_COMPANY_EXCEL_DOWNLOAD = '/license/admin/excel/export',
  GET_EXCEL_LIST_FILTER = '/license/admin/excel/list-filter',
}

export default urlMap;

import { DefaultPagination, noSearchPagination } from '../interface';
import request from '../request';
import UrlMap from './urlMap';
const queryAdminList = () => {
  return request({
    url: UrlMap.QUERY_ADMIN_LIST,
    method: 'post',
  });
};
const queryRecentActions = () => {
  return request({
    url: UrlMap.QUERY_RECENT_ACTIONS,
    method: 'get',
  });
};
const getAuthGroupsList = (
  data: { groupName?: string } & noSearchPagination
) => {
  return request({
    url: UrlMap.GET_AUTH_GROUPS_LIST,
    method: 'get',
    params: {
      ...data,
    },
  });
};

const delGroups = (data: { idList: React.Key[] }) => {
  return request({
    url: UrlMap.DEL_AUTH_GROUP,
    method: 'post',
    data,
  });
};

const getRelationList = (data: { idList: React.Key[] }) => {
  return request({
    url: UrlMap.GET_RELATION_LIST,
    method: 'post',
    data,
  });
};
const getPermissionList = () => {
  return request({
    url: UrlMap.GET_PERMISSION_LIST,
    method: 'post',
  });
};

const addGroup = (data: { groupName: string; permissionList: React.Key[] }) => {
  return request({
    url: UrlMap.POST_ADD_GROUP,
    method: 'post',
    data,
  });
};

const getGroupDetail = ({ groupId }: { groupId: string }) => {
  return request({
    url: UrlMap.GET_GROUP_DETAIL,
    method: 'get',
    params: {
      groupId,
    },
  });
};

const editGroup = (data: {
  id: number;
  name: string;
  permissionList: number[];
}) => {
  return request({
    url: UrlMap.POST_EDIT_GROUP,
    method: 'post',
    data,
  });
};

export {
  queryAdminList,
  queryRecentActions,
  getAuthGroupsList,
  delGroups,
  getRelationList,
  getPermissionList,
  addGroup,
  getGroupDetail,
  editGroup,
};

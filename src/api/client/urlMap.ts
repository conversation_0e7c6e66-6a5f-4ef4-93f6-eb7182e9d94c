const enum UrlMap {
  'QUERY_COMPANY_PAGE' = '/license/company/page',
  'POST_COMPANY_ADD' = '/license/company/add',
  'DEL_COMPANY_DELETE' = '/license/company/delete',
  'GET_COMPANY_DETAIL' = '/license/company/detail',
  'POST_COMPANY_EDIT' = '/license/company/edit',
  'POST_COMPANY_EDITLOG_DETAIL' = '/license/company/edit-log/detail',
  'GET_COMPANY_EDITLOG_PAGE' = '/license/company/edit-log/page',
  'DEL_COMPANY_POOL_DELETE' = '/license/company/pool/delete',
  'GET_COMPANY_POOL_DETAIL' = '/license/company/pool/detail',
  'QUERY_COMPANY_POOL_PAGE' = '/license/company/pool/page',
  'POST_COMPANY_POOL_SUBMIT' = '/license/company/pool/submit',
  'COMPANY_RELATION_LIST' = '/license/company/relation/list',
  'QUERY_USER_EDITLOG_PAGE' = '/license/user/edit-log/page',
  'POST_USER_DETAIL' = '/license/user/edit-log/detail',
  'QUERY_USER_PAGE' = '/license/user/page',
  'DEL_USER_DELETE' = '/license/user/delete',
  'POST_USER_RELATION_LIST' = '/license/user/relation/list',
  'POST_ADD_USER' = '/license/user/add',
  'POST_EDIT_USER' = '/license/user/edit',
  'GET_USER_DETAIL' = '/license/user/detail',
}
export default UrlMap;

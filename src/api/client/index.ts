import qs from 'qs';
import { noSearchPagination } from '../interface';
import request from '../request';
import UrlMap from './urlMap';

export type companyDetail = {
  comment?: string;
  companyDomainList?: any[];
  description?: string;
  email?: string;
  name?: string;
  id?: number;
};

export type companyPool = {
  allocated: number;
  ampconExpireDate: string;
  comments: string;
  companyId: number;
  id: number;
  name: string;
  switchExpireDate: string;
};

export type userDetail = {
  companyIdList?: React.Key[];
  email?: string;
  groupIdList?: React.Key[];
  password?: string;
  passwordConfirm?: string;
  username?: string;
  country?: string;
  firstName?: string;
  isActive?: number;
  isSuperuser?: number;
  lastName?: string;
  telephone?: string;
  userId?: number;
};

const queryCompanyPage = (
  data: { companyName?: string } & noSearchPagination
) => {
  return request({
    url: UrlMap.QUERY_COMPANY_PAGE,
    method: 'get',
    params: data,
  });
};

const getCompanyDetail = (data: { companyId?: React.Key }) => {
  return request({
    url: UrlMap.GET_COMPANY_DETAIL,
    method: 'get',
    params: data,
  });
};

const postAddCompanyDetail = (data: companyDetail) => {
  return request({
    url: UrlMap.POST_COMPANY_ADD,
    method: 'post',
    data,
  });
};

const postEditCompanyDetail = (
  data: companyDetail & {
    deleteDomainIdList: any[];
    id: React.Key;
    licenseLimitationList: any[];
  }
) => {
  return request({
    url: UrlMap.POST_COMPANY_EDIT,
    method: 'post',
    data,
  });
};

const getCompanyEditlogPage = (
  data: { keyword?: string } & noSearchPagination
) => {
  return request({
    url: UrlMap.GET_COMPANY_EDITLOG_PAGE,
    method: 'get',
    params: data,
  });
};

const companyRelationList = (data: { idList: React.Key[] }) => {
  return request({
    url: UrlMap.COMPANY_RELATION_LIST,
    method: 'post',
    data,
  });
};
const delCompany = (data: { idList: React.Key[] }) => {
  return request({
    url: UrlMap.DEL_COMPANY_DELETE,
    method: 'post',
    data,
  });
};

const getCompanyPoolPage = (
  data: { keyword?: string } & noSearchPagination
) => {
  return request({
    url: UrlMap.QUERY_COMPANY_POOL_PAGE,
    method: 'get',
    params: data,
    paramsSerializer: (params) => {
      return qs.stringify(params, { allowDots: true });
    },
  });
};

const getCompanyEditPoolDetail = (data: {
  companyPoolId: number | undefined;
}) => {
  return request({
    url: UrlMap.GET_COMPANY_POOL_DETAIL,
    method: 'get',
    params: data,
  });
};

const postCompanyPollDetailEdit = (data: companyPool) => {
  return request({
    url: UrlMap.POST_COMPANY_POOL_SUBMIT,
    method: 'post',
    data,
  });
};

const postCompanyEditLogsDetail = (data: { logId: number | undefined }) => {
  return request({
    url: UrlMap.POST_COMPANY_EDITLOG_DETAIL,
    method: 'post',
    params: data,
  });
};

const delCompanyPool = (data: { idList: React.Key[] }) => {
  return request({
    url: UrlMap.DEL_COMPANY_POOL_DELETE,
    method: 'post',
    data,
  });
};

const getUserEditLogsPage = (
  data: { keyword?: string } & noSearchPagination
) => {
  return request({
    url: UrlMap.QUERY_USER_EDITLOG_PAGE,
    method: 'get',
    params: data,
  });
};
const postUserEditLogsDetail = (data: { logId: number | undefined }) => {
  return request({
    url: UrlMap.POST_USER_DETAIL,
    method: 'post',
    params: data,
  });
};

const getUserPageList = (
  data: { username?: string; userType: number } & noSearchPagination
) => {
  return request({
    url: UrlMap.QUERY_USER_PAGE,
    method: 'post',
    data,
  });
};

const delUser = (data: { idList: React.Key[] }) => {
  return request({
    url: UrlMap.DEL_USER_DELETE,
    method: 'post',
    data,
  });
};

const userRelationList = (data: { idList: React.Key[] }) => {
  return request({
    url: UrlMap.POST_USER_RELATION_LIST,
    method: 'post',
    data,
  });
};

const postAddUser = (data: userDetail) => {
  return request({
    url: UrlMap.POST_ADD_USER,
    method: 'post',
    data,
  });
};

const postEditUser = (data: userDetail) => {
  return request({
    url: UrlMap.POST_EDIT_USER,
    method: 'post',
    data,
  });
};

const getUserDetail = (data: { userId: number | undefined }) => {
  return request({
    url: UrlMap.GET_USER_DETAIL,
    method: 'get',
    params: data,
  });
};

export {
  queryCompanyPage,
  getCompanyDetail,
  postAddCompanyDetail,
  postEditCompanyDetail,
  delCompany,
  getCompanyEditlogPage,
  postCompanyEditLogsDetail,
  companyRelationList,
  getCompanyPoolPage,
  getCompanyEditPoolDetail,
  postCompanyPollDetailEdit,
  delCompanyPool,
  getUserEditLogsPage,
  postUserEditLogsDetail,
  getUserPageList,
  delUser,
  userRelationList,
  getUserDetail,
  postAddUser,
  postEditUser,
};

// 通过获取redux中的权限列表，判断当前路由是否有权限
import { selectPermission } from '@/store/feature/userSlice';
import { useAppSelector } from '@/store/hooks';
import { useLocation } from 'react-router-dom';

interface UsePermission {
  hasAdd: boolean;
  hasChange: boolean;
  hasDelete: boolean;
}

export default function usePermission(): UsePermission {
  const { pathname } = useLocation();
  const permission = useAppSelector(selectPermission);
  let defaultPermission = {
    hasAdd: false,
    hasChange: false,
    hasDelete: false,
  };
  for (let i = 0; i < permission.length; i++) {
    const item = permission[i];
    const currentRoute = item.menuList.some((menu) => {
      if (menu.route === pathname) {
        defaultPermission = {
          ...menu.permission,
        };
        return true;
      }
      return false;
    });
    if (currentRoute) {
      break;
    }
  }
  return defaultPermission;
}

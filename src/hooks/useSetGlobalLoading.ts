import { changeLoading } from '@/store/feature/appSlice';
import { useAppDispatch } from '@/store/hooks';
import { useEffect } from 'react';

function useSetGlobalLoading(loading: boolean) {
  const dispatch = useAppDispatch();
  useEffect(() => {
    dispatch(changeLoading(loading));
    return () => {
      dispatch(changeLoading(false));
    };
  }, [loading]);
}
export default useSetGlobalLoading;

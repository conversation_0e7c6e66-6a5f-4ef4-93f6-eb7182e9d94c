type PathMapType = {
  [key in number]: string;
};
export const pathMap: PathMapType = {
  2: 'Permission',
  3: '/admin/auth/group',
  4: 'ContentType',
  8: '/admin/client/company',
  9: '/admin/client/user',
  12: '/admin/license/featuretype',
  13: '/admin/license/licenses',
  15: 'CompanyDomain',
  16: '/admin/license/licenselimitationlog',
  17: '/admin/license/licensesummarydetail',
  19: '/admin/license/extendexpirationdateofbatchlicense',
  20: '/admin/license/licensedeletelog',
  21: '/admin/license/licensesummarylog',
  22: '/admin/license/batchextendlog',
  23: '/admin/license/licensereporting',
  24: '/admin/license/licenseeditlog',
  25: '/admin/client/usereditlog',
  26: '/admin/client/companyeditlog',
  27: '/admin/client/companypool',
  28: '/admin/license/poollicense',
  29: '/admin/ampcon/parameterconfiguration',
  30: '/admin/ampcon/ampconlicenses',
  31: '/admin/client/companyampconauthorization',
};

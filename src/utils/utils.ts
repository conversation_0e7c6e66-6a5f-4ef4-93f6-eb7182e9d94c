import { AxiosResponse } from 'axios';
import Cookies from 'js-cookie';

/**
 * Generates a UUID.
 *
 * @return {string} The generated UUID.
 */
export const generateUUID = () => {
  let d = new Date().getTime();
  if (
    window &&
    window.performance &&
    typeof window.performance.now === 'function'
  ) {
    d += performance.now();
  }
  let uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(
    /[xy]/g,
    function (c) {
      const r = (d + Math.random() * 16) % 16 | 0;
      d = Math.floor(d / 16);
      return (c == 'x' ? r : (r & 0x3) | 0x8).toString(16);
    }
  );

  uuid = uuid.replace(/-/g, '');
  return uuid;
};

export const downloadByBlob = (res: AxiosResponse, name?: string) => {
  // const blob = new Blob([res.data], {
  //   type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  // });
  const blob = res.data;
  const fileName =
    res.headers['content-disposition']?.split('=')[1] ||
    name ||
    'download.xlsx';
  const downloadElement = document.createElement('a');
  const href = window.URL.createObjectURL(blob);
  downloadElement.href = href;
  downloadElement.download = fileName.replace(/"/g, '');
  document.body.appendChild(downloadElement);
  downloadElement.click();
  document.body.removeChild(downloadElement);
  window.URL.revokeObjectURL(href);
};

export const setCookieWithToken = (token: string) => {
  // 设置父域名下的cookie
  Cookies.set('picaToken', token, {
    domain: import.meta.env.VITE_COOKIE_DOMAIN,
  });
};

// 防抖函数
export function debounce(func: (...args: any[]) => void, wait: number) {
  let timeout: any;
  return function (this: void, ...args: any) {
    clearTimeout(timeout);
    timeout = setTimeout(() => {
      func.apply(this, args);
    }, wait);
  };
}

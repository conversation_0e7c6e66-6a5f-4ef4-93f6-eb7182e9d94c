import React, { useRef, useState } from 'react';
import styles from './index.module.scss';
import Table from '@/components/Table';
import { ColumnsType } from 'antd/es/table';
import TableHead from './components/TableHead';
import { useQuery } from 'react-query';
import {
  LicensesPageParams,
  addHwIds,
  getExpirationDateById,
  getUserAmpconLicensesListFilter,
  getUserHwIdsById,
  postUserAmpconLicensesList,
} from '@/api/ampcon';
import {
  CopyOutlined,
  DeleteOutlined,
  EditOutlined,
  FileDoneOutlined,
  PlusCircleFilled,
  VerticalAlignBottomOutlined,
} from '@ant-design/icons';
import {
  ConfigProvider,
  Form,
  Input,
  message,
  Modal,
  Popover,
  Select,
  Spin,
  theme,
} from 'antd';
import EditableCell from './components/EditableCell';
import { debounce } from '@/utils/utils';

export interface DataType {
  companyId: number;
  companyName: string;
  createTime: string;
  deviceType: number;
  deviceTypeName: string;
  featureType: number;
  featureTypeName: string;
  id: number;
  licenseName: string;
  pattern: string;
  softwareType: number;
  softwareTypeName: string;
  softwareVersion: number;
  softwareVersionName: string;
  totalHwIdsNum: number;
  licenseKey: string;
}
function index() {
  const [tableParams, setTableParams] = useState<LicensesPageParams>({
    current: 1,
    size: 10,
  });
  const { data, refetch } = useQuery(
    ['parameter list', tableParams],
    () => postUserAmpconLicensesList(tableParams),
    {
      select: (res) => res.data,
    }
  );
  const { data: optionsData } = useQuery(
    'getFilter',
    () => getUserAmpconLicensesListFilter(),
    {
      select: (res) => res.data as any,
    }
  );
  const columns: ColumnsType<DataType> = [
    {
      title: 'License id',
      dataIndex: 'id',
      width: 80,
      fixed: 'left',
    },
    {
      title: 'License name',
      dataIndex: 'licenseName',
      width: 150,
      fixed: 'left',
      render: (text, record) => (
        <EditableCell record={record} editable={true} handleSave={handleSave} />
      ),
    },
    {
      title: 'License key',
      dataIndex: 'licenseKey',
      render: (text, record) => (
        <div className={styles.license_key_wrap}>
          <Popover
            content={
              <p style={{ width: 500, wordBreak: 'break-all' }}>{text}</p>
            }
          >
            <div className={styles.text_wrap}>{text}</div>
          </Popover>
          <div className={styles.option}>
            <div className={styles.action_btn} onClick={() => copyKey(text)}>
              <CopyOutlined />
              <span className={styles.font_orange}>Copy</span>
            </div>
            <div
              className={styles.action_btn}
              onClick={() => downloadText(record)}
            >
              <VerticalAlignBottomOutlined />
              <span className={styles.font_orange}>Download</span>
            </div>
          </div>
        </div>
      ),
      width: 250,
      fixed: 'left',
    },
    {
      title: 'Total items',
      dataIndex: 'totalHwIdsNum',
      render: (text, record) => (
        <div
          className={styles.hw_text}
          onClick={(e) => handleOpenModal(record)}
        >
          <span className={styles.num_text}>{text}</span>
          <EditOutlined style={{ color: '#ef5a28', marginLeft: '8px' }} />
        </div>
      ),
    },
    {
      title: 'Create time',
      dataIndex: 'createTime',
    },
    {
      title: 'Company',
      dataIndex: 'companyName',
    },
    {
      title: 'Software type',
      dataIndex: 'softwareTypeName',
    },
    {
      title: 'Software version',
      dataIndex: 'softwareVersionName',
    },
    {
      title: 'Device type',
      dataIndex: 'deviceTypeName',
    },
    {
      title: 'Feature type',
      dataIndex: 'featureTypeName',
    },
    {
      title: 'License type',
      dataIndex: 'licenseTypeName',
    },
    {
      title: 'Mode',
      dataIndex: 'pattern',
    },
  ];
  // licenseName 编辑
  const handleSave = () => {
    refetch();
  };
  //复制licenseKey
  const copyKey = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      message.success('Copy Success', 2);
    } catch (err) {
      console.error('Copy failed', err);
    }
  };
  // 下载文本为lic格式
  const downloadText = (record: DataType) => {
    const element = document.createElement('a');
    const file = new Blob([record.licenseKey], {
      type: 'text/plain;charset=utf-8',
    });
    element.href = URL.createObjectURL(file);
    element.download = `${record.licenseName || record.id}.lic`;
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
  };

  const [modalVisible, setModalVisible] = useState(false);
  const [form] = Form.useForm();
  const currentId = useRef(0);
  const currentName = useRef('');
  const isAmpconT = useRef(false);
  const handleOpenModal = (record: DataType) => {
    currentId.current = record.id;
    isAmpconT.current = record.softwareType === 2;
    currentName.current = record.licenseName;
    setModalVisible(true);
  };
  const { data: hwData, isLoading: hwLoading } = useQuery(
    ['getHwList', modalVisible, currentId.current],
    () => getUserHwIdsById(currentId.current),
    {
      enabled: modalVisible,
      select: (res) => res.data as any,
    }
  );
  const { data: expirationDate } = useQuery(
    ['getExpirationDate', modalVisible],
    () => getExpirationDateById(currentId.current),
    {
      enabled: modalVisible,
      select: (res) => res.data as any,
    }
  );
  const handleHwSave = async () => {
    if (form.getFieldValue('details') === undefined) {
      return setModalVisible(false);
    }
    const values = await form.validateFields();
    console.log(values);
    const res = await addHwIds({ ...values, id: currentId.current });
    if (res.code === 200) {
      message.success('Save Success');
      form.resetFields();
      refetch();
      setModalVisible(false);
    }
  };
  const onValuesChange = debounce((changed: any, allValues: any) => {
    let expireTime = undefined;
    let createTime = undefined;
    if (Array.isArray(allValues.createTime)) {
      createTime = [
        allValues.createTime[0].format('YYYY-MM-DD HH:mm:ss'),
        allValues.createTime[1].format('YYYY-MM-DD HH:mm:ss'),
      ];
    }
    if (Array.isArray(allValues.expireTime)) {
      expireTime = [
        allValues.expireTime[0].format('YYYY-MM-DD'),
        allValues.expireTime[1].format('YYYY-MM-DD'),
      ];
    }
    setTableParams({ ...tableParams, ...allValues, expireTime, createTime });
  }, 500);
  const downloadHardFile = (data: any) => {
    const record = data.hardwareFileContent;
    const fileName = data.fileName;
    const element = document.createElement('a');
    const file = new Blob([record], {
      type: 'text/plain;charset=utf-8',
    });
    element.href = URL.createObjectURL(file);
    element.download = fileName;
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
  };
  return (
    <div className={styles.ampcon_container}>
      <h1 className={styles.title}>AmpCon Licenses</h1>
      <Table
        scroll={{ x: 1200 }}
        columns={columns}
        dataSource={data?.records || []}
        title={() => (
          <TableHead data={optionsData} onValuesChange={onValuesChange} />
        )}
        pagination={{
          size: 'small',
          total: data?.total || 0,
          current: tableParams.current,
          showSizeChanger: true,
          showTotal: (total) => `Total ${total} items`,
          onChange: (page, pageSize) => {
            setTableParams({
              ...tableParams,
              current: page,
              size: pageSize,
            });
          },
        }}
      ></Table>
      <ConfigProvider
        theme={{
          algorithm: [theme.defaultAlgorithm],
        }}
      >
        <Modal
          title="Total items"
          open={modalVisible}
          okText="Save"
          onCancel={() => {
            setModalVisible(false);
            form.resetFields();
          }}
          getContainer={false}
          onOk={handleHwSave}
        >
          <div className={styles.hw_list_container}>
            <Spin size="large" spinning={hwLoading}>
              <div className={styles.title_info}>
                <span>License ID: {currentId.current}</span>
                <span>License Name: {currentName.current}</span>
              </div>
              <Form form={form}>
                <Form.List name="details">
                  {(fields, { add, remove }) => (
                    <>
                      {!isAmpconT.current && (
                        <div className={styles.add_btn_wrap}>
                          <span>Add Device</span>
                          <PlusCircleFilled
                            onClick={() => {
                              // add({ hardwareIds: '', expireDate: '' }, 0);
                              add();
                            }}
                            style={{
                              cursor: 'pointer',
                              color: '#ef5a28',
                              marginLeft: '16px',
                            }}
                          />
                        </div>
                      )}
                      <div className={styles.hw_list}>
                        {fields.map((field, index) => (
                          <div className={styles.hw_item} key={field.key}>
                            <Form.Item
                              name={[field.name, 'hardwareIds']}
                              rules={[
                                {
                                  required: true,
                                  message: 'Please input hardware id',
                                },
                              ]}
                            >
                              <Input.TextArea
                                placeholder="Support multiple hw-ids, please use ; to separate"
                                rows={4}
                                style={{ width: 196 }}
                              />
                            </Form.Item>
                            <Form.Item
                              name={[field.name, 'expireDate']}
                              rules={[
                                {
                                  required: true,
                                  message: 'Please select expiration date',
                                },
                              ]}
                            >
                              <Select
                                placeholder="Select expiration date"
                                style={{ height: 98, width: 196 }}
                              >
                                {expirationDate.map((item: string) => (
                                  <Select.Option key={item} value={item}>
                                    {item}
                                  </Select.Option>
                                ))}
                              </Select>
                            </Form.Item>
                            {fields.length > 1 && (
                              <DeleteOutlined
                                style={{
                                  fontSize: '20px',
                                  cursor: 'pointer',
                                }}
                                onClick={() => remove(index)}
                              />
                            )}
                          </div>
                        ))}
                        {hwData?.map((item: any, index: number) => (
                          <div key={index} className={styles.hw_item}>
                            {item.hardwareId.length > 0 ? (
                              <>
                                <Input.TextArea
                                  disabled
                                  rows={4}
                                  value={item.hardwareId}
                                  style={{ marginBottom: 16 }}
                                ></Input.TextArea>
                                <Input
                                  style={{ height: 98, marginBottom: 16 }}
                                  value={item.stringExpireDate}
                                  disabled
                                ></Input>
                                <span>
                                  {item.hasFailure === 1 ? 'Failure' : 'Normal'}
                                </span>
                              </>
                            ) : (
                              <div className={styles.column_wrap}>
                                <div
                                  className={styles.file_wrap}
                                  onClick={() => downloadHardFile(item)}
                                >
                                  <FileDoneOutlined
                                    style={{ color: '#ef5a28' }}
                                  />
                                  <span className={styles.file_name}>
                                    {item?.fileName}
                                  </span>
                                </div>
                                <p>{item.stringExpireDate}</p>
                                {/* <Input disabled value={item.deviceId} />
                                <Input disabled value={item.systemMac} />
                                <Input disabled value={item.stringExpireDate} /> */}
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </>
                  )}
                </Form.List>
              </Form>
            </Spin>
          </div>
        </Modal>
      </ConfigProvider>
    </div>
  );
}

export default index;

.ampcon_container {
  width: 80%;
  .title {
    padding: 50px 0;
    color: $fontColorGrey;
    font-size: 35px;
    text-align: center;
  }
}
.license_key_wrap {
  display: flex;
  align-items: center;
  .text_wrap {
    width: 80px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;
    &:hover {
      text-decoration: underline;
    }
  }
  .option {
    margin-left: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
    .action_btn {
      display: flex;
      gap: 8px;
      align-items: center;
      font-size: 14px;
      cursor: pointer;
      .font_orange {
        color: $colorPrimary;
      }
    }
  }
}
.hw_text {
  cursor: pointer;
  &:hover {
    text-decoration: underline;
  }
}
.hw_list_container {
  min-height: 380px;
  .title_info {
    padding: 16px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 16px;
  }
  .add_btn_wrap {
    padding: 16px 0;
  }
  .hw_list {
    max-height: 350px;
    overflow: auto;
    .hw_item {
      display: flex;
      gap: 16px;
      align-items: center;
    }
  }
  .column_wrap {
    width: 100%;
    display: flex;
    gap: 16px;
    flex-direction: column;
  }
  .form_item {
    display: flex;
    gap: 16px;
  }
}
.table_header {
  display: flex;
  flex-direction: column;
  .header_top {
    padding-top: 10px;
    .nav_wrap {
      display: flex;
      align-items: center;
      padding: 16px 0 0 0;
      font-weight: bolder;
      gap: 36px;
    }
  }
  .filter_head {
    gap: 16px;
    :global(.ant-form-item) {
      margin-inline-end: 0px;
    }
  }
}

.file_wrap {
  cursor: pointer;
  .file_name {
    margin-left: 8px;
    color: $colorPrimary;
    &:hover {
      text-decoration: underline;
    }
  }
}

export interface LicensesData {
  featureType: string;
  featureTypeId: number;
  licenseDataList: LicensesDataItem[];
  limit: number;
  mode: string;
  remain: number;
  speedType: string;
  speedTypeId: number;
}

export interface PoolLicenseList {
  companyId: number;
  companyName: string;
  licenseList: LicensesDataItem[];
  limit: number;
  remain: number;
}
export interface LicensesDataItem {
  companyId: number;
  companyName: string;
  createTime: string;
  expirationDate: string;
  featureType: string;
  hardwareId: string;
  id: number;
  licenseKey: string;
  mode: string;
  name: string;
  siteName: string;
  speedType: string;
}

export interface TokenListData {
  comments: string;
  createTime: string;
  id: number;
  token: string;
  userId: number;
  username: string;
}

export interface SummaryListItemData {
  assigned: number;
  comments: string;
  company: string;
  createTime: string;
  expirationDate: string;
  featureType: string;
  id: number;
  mode: string;
  remaining: number;
  speedType: string;
  speedTypeId: number;
  featureTypeId: number;
}

import React, { useEffect, useState } from 'react';
import {
  Button,
  Form,
  Input,
  Radio,
  Select,
  Upload,
  UploadProps,
  message,
} from 'antd';
import FormCom from '../components/FormCom';
import styles from '@/pages/Admin/Ampcon/Licenses/Detail/index.module.scss';
import { useQuery } from 'react-query';
import {
  AmpconExpirationDateParams,
  downloadUserAmpconTemplate,
  getUserAmpconLicenseDetailOptions,
  getUserAmpconLicensesExpirationDate,
  saveUserAmpconLicenses,
  analysisExcel,
} from '@/api/ampcon';
import {
  DeleteOutlined,
  DownOutlined,
  PlusCircleFilled,
} from '@ant-design/icons';
import { downloadByBlob } from '@/utils/utils';
import { useNavigate } from 'react-router-dom';

const paramsMap = [
  'deviceType',
  'featureType',
  'licenseType',
  'softwareType',
  'softwareVersion',
];

function index() {
  const [form] = Form.useForm();
  const softType = Form.useWatch('softwareType', form);
  const licenseType = Form.useWatch('licenseType', form);
  const router = useNavigate();
  const { data: filterOptions } = useQuery(
    'licenses',
    () => getUserAmpconLicenseDetailOptions(),
    {
      select: (data) => data.data,
    }
  );

  useEffect(() => {
    form.setFieldsValue({
      details: [{ hardwareIds: '', expireDate: '' }],
    });
  }, []);

  const [softVersionOptions, setSoftVersionOptions] = useState([]);
  const [deviceTypeOptions, setDeviceTypeOptions] = useState([]);
  const [featureTypeOptions, setFeatureTypeOptions] = useState([]);
  const [expirationDate, setExpirationDate] = useState([]);
  const [parsedHardwareIds, setParsedHardwareIds] = useState<string[]>([]);
  const [parsing, setParsing] = useState(false);

  useEffect(() => {
    if (filterOptions && softType) {
      const list = filterOptions.filterList.find(
        (item: any) => item.id === softType
      ).list;
      list.forEach((item: any) => {
        if (item.fieldName === 'Software version') {
          setSoftVersionOptions(item.children);
        }
        if (item.fieldName === 'Device type') {
          setDeviceTypeOptions(item.children);
        }
        if (item.fieldName === 'Feature type') {
          setFeatureTypeOptions(item.children);
        }
      });
    }
    form.setFieldsValue({
      pattern: filterOptions?.modeList[0].name,
    });
  }, [softType, filterOptions]);

  const handleValuesChange = (changedValues: any, allValues: any) => {
    console.log(changedValues, allValues);
    const valuesKeys = Object.keys(allValues);
    const params: { [key: string]: any } = {};
    valuesKeys.forEach((key) => {
      if (paramsMap.includes(key)) {
        params[key] = allValues[key];
      }
    });
    if (
      [1, 10].includes(allValues.softwareType) &&
      allValues.licenseType === 1
    ) {
      if (changedValues.softwareVersion) {
        return;
      }
      params.deviceType = undefined;
      params.featureType = undefined;
      form.setFieldsValue({
        deviceType: undefined,
        featureType: undefined,
      });
    } else if (allValues.softwareType === 2) {
      params.softwareVersion = undefined;
      params.deviceType = undefined;
      form.setFieldsValue({
        softwareVersion: undefined,
        deviceType: undefined,
      });
    }
    Object.keys(changedValues).forEach((key) => {
      if (paramsMap.includes(key)) {
        fetchExpirationDate(params as AmpconExpirationDateParams);
      }
    });
  };
  const fetchExpirationDate = async (data: AmpconExpirationDateParams) => {
    form.setFieldsValue({
      expireDate: undefined,
    });
    const res = await getUserAmpconLicensesExpirationDate(data);
    setExpirationDate(res.data);
  };

  const handleBatchFillExpireDate = (index: number) => {
    const details = form.getFieldValue('details');
    const currentExpireDate = details[index]?.expireDate;

    if (currentExpireDate) {
      let filledCount = 0;
      const newDetails = details.map((item: any, i: number) => {
        if (i !== index && !item.expireDate) {
          filledCount++;
          return { ...item, expireDate: currentExpireDate };
        }
        return item;
      });

      if (filledCount > 0) {
        form.setFieldsValue({ details: newDetails });
        message.success(`Successfully filled ${filledCount} items.`);
      } else {
        message.info('All other items already have an expiration date.');
      }
    } else {
      message.warning('Please select an expiration date to fill from.');
    }
  };

  // 展开更多
  const [showMore, setShowMore] = useState(false);
  const showMoreChange = () => {
    setShowMore((prevShowMore) => !prevShowMore);
  };

  // 上传文件配置
  const uploadProps: UploadProps = {
    accept: '.xlsx, .xls,.lic',
    maxCount: 1,
    multiple: false,
    beforeUpload: (file) => {
      if (file.size > 1024 * 1024 * 2) {
        message.error('File must be smaller than 2MB!');
      }
      return false;
    },
  };
  const [downloading, setDownloading] = useState(false);

  // 下载空白模板
  const downloadBlankTemplate = async () => {
    setDownloading(true);
    const res = await downloadUserAmpconTemplate().finally(() =>
      setDownloading(false)
    );
    downloadByBlob(res);
  };

  const [loading, setLoading] = useState(false);
  const handleSubmit = async (values: any) => {
    console.log(values);
    setLoading(true);
    const formData = new FormData();
    Object.keys(values).forEach((key) => {
      if (values[key] === undefined) {
        return;
      }
      if (key === 'file' && Array.isArray(values.file)) {
        return formData.append('file', values.file[0].originFileObj);
      }
      if (key === 'details' && Array.isArray(values.details)) {
        return values.details.forEach((item: any, index: number) => {
          formData.append(`details[${index}].hardwareIds`, item.hardwareIds);
          formData.append(`details[${index}].expireDate`, item.expireDate);
        });
      }
      if (softType === 2) {
        formData.append('hasFile', 'true');
      }
      formData.append(key, values[key]);
    });
    formData.append('pattern', 'AmpCon');
    const res = await saveUserAmpconLicenses(formData).finally(() =>
      setLoading(false)
    );
    console.log(res);
    if (res.code === 200) {
      if (res.data.isSuccess) {
        message.success('Save success', 2).then(() => router(-1));
      } else {
        if (res.data.errorList.length > 0) {
          const errors = res.data.errorList.map(
            (item: any) => item.errorMsg[0]
          );
          message.error(errors.join(' '), 5);
        } else {
          message.error(res.data.message);
        }
      }
    }
  };

  const parseExcelProps: UploadProps = {
    accept: '.xlsx, .xls',
    showUploadList: false,
    customRequest: async (options) => {
      const { file, onSuccess, onError } = options;
      const formData = new FormData();
      formData.append('file', file as File);
      setParsing(true);
      try {
        const res = await analysisExcel(formData);
        setParsedHardwareIds((res.data as string[]) || []);
        message.success('解析成功');
        if (onSuccess) onSuccess(undefined);
        form.setFieldsValue({
          details: res.data.map((item: string) => ({
            hardwareIds: item,
          })),
        });
        setShowMore(true);
      } catch (err) {
        message.error('解析失败');
        if (onError) onError(err as Error);
      } finally {
        setParsing(false);
      }
    },
  };
  return (
    <FormCom pageTitle="AmpCon license add form">
      <Form
        form={form}
        size="large"
        style={{ width: '860px' }}
        layout="vertical"
        onValuesChange={handleValuesChange}
        onFinish={handleSubmit}
        onFinishFailed={({ values, errorFields, outOfDate }) => {
          console.log(values, errorFields, outOfDate);
        }}
      >
        <div className={styles.form_inline_wrap}>
          <Form.Item
            name="softwareType"
            label="Software Type"
            rules={[{ required: true, message: 'Please input software type!' }]}
          >
            <Select>
              {filterOptions?.filterList.map((item: any) => (
                <Select.Option key={item.id} value={item.id}>
                  {item.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
          {softType !== 2 && (
            <>
              <Form.Item
                name="softwareVersion"
                label="Software Version"
                rules={[
                  {
                    required: true,
                    message: 'Please select software version!',
                  },
                ]}
              >
                <Select>
                  {softVersionOptions.map((item: any) => (
                    <Select.Option key={item.id} value={item.id}>
                      {item.name}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
              {licenseType !== 1 && (
                <Form.Item
                  name="deviceType"
                  label="Device Type"
                  rules={[
                    { required: true, message: 'Please select device type!' },
                  ]}
                >
                  <Select>
                    {deviceTypeOptions.map((item: any) => (
                      <Select.Option key={item.id} value={item.id}>
                        {item.name}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              )}
            </>
          )}
          {(licenseType !== 1 || ![1, 10].includes(softType)) && (
            <Form.Item
              name="featureType"
              label="Feature Type"
              rules={[
                { required: true, message: 'Please select feature type!' },
              ]}
            >
              <Select>
                {featureTypeOptions.map((item: any) => (
                  <Select.Option key={item.id} value={item.id}>
                    {item.name}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          )}
        </div>
        <Form.Item
          label="License type"
          name="licenseType"
          rules={[{ required: true, message: 'Please select license type!' }]}
        >
          <Select>
            {filterOptions?.licenseTypeList.map((item: any) => (
              <Select.Option key={item.id} value={item.id}>
                {item.name}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item
          label="License name"
          name="licenseName"
          extra="The 'License name' is an optional field that helps you differentiate between your licenses."
        >
          <Input />
        </Form.Item>
        {softType !== 2 ? (
          <>
            <Form.Item
              label="Addition Method"
              name="hasFile"
              required
              initialValue={false}
            >
              <Radio.Group>
                <Radio value={false}>Form input</Radio>
                <Radio value={true}>File upload</Radio>
              </Radio.Group>
            </Form.Item>
            <Form.Item noStyle shouldUpdate>
              {() => {
                const hasFile = form.getFieldValue('hasFile');
                return hasFile ? (
                  <>
                    <div className={styles.form_list_label}>
                      <span className={styles.mark}>*</span> Hardware id &
                      Expiration date{' '}
                    </div>
                    <div className={styles.form_upload_wrap}>
                      <Form.Item
                        name="file"
                        noStyle
                        valuePropName="fileList"
                        getValueFromEvent={(e) => e.fileList}
                        rules={[
                          { required: true, message: 'Please upload file!' },
                        ]}
                      >
                        <Upload {...uploadProps}>
                          <Button type="primary">Upload</Button>
                        </Upload>
                      </Form.Item>
                      <div className={styles.download_btn}>
                        <Button
                          type="primary"
                          ghost
                          onClick={downloadBlankTemplate}
                          loading={downloading}
                        >
                          Blank template
                        </Button>
                      </div>
                    </div>
                  </>
                ) : (
                  <Form.List name="details">
                    {(fields, { add, remove }) => (
                      <>
                        <div className={styles.form_list_label}>
                          <span className={styles.mark}>*</span> Hardware id &
                          Expiration date{' '}
                          {[1, 10].includes(softType) &&
                          licenseType === 1 ? null : (
                            <>
                              <PlusCircleFilled
                                onClick={() => {
                                  add({ hardwareIds: '', expireDate: '' }, 0);
                                }}
                                style={{
                                  cursor: 'pointer',
                                  color: '#ef5a28',
                                  marginLeft: '16px',
                                }}
                              />
                              <Upload {...parseExcelProps}>
                                <Button type="primary" loading={parsing}>
                                  Analysis Excel
                                </Button>
                              </Upload>
                            </>
                          )}
                        </div>
                        {fields
                          .slice(0, showMore ? fields.length : 3)
                          .map((field, index) => (
                            <div
                              className={styles.form_list_wrap}
                              key={field.key}
                            >
                              <Form.Item
                                name={[field.name, 'hardwareIds']}
                                rules={[
                                  {
                                    required: true,
                                    message: 'Please input hardware id',
                                  },
                                ]}
                              >
                                <Input.TextArea
                                  placeholder="Support multiple hw-ids, please use , or ; to separate"
                                  rows={4}
                                />
                              </Form.Item>
                              <div style={{ display: 'flex', gap: '8px' }}>
                                <Form.Item
                                  style={{ flex: 1 }}
                                  name={[field.name, 'expireDate']}
                                  rules={[
                                    {
                                      required: true,
                                      message: 'Please select expiration date',
                                    },
                                  ]}
                                >
                                  <Select
                                    placeholder="Select expiration date"
                                    style={{ height: '116px' }}
                                  >
                                    {expirationDate.map((item: string) => (
                                      <Select.Option key={item} value={item}>
                                        {item}
                                      </Select.Option>
                                    ))}
                                  </Select>
                                </Form.Item>
                                <Button
                                  onClick={() =>
                                    handleBatchFillExpireDate(index)
                                  }
                                  style={{
                                    alignSelf: 'center',
                                  }}
                                >
                                  Batch-Sync
                                </Button>
                              </div>
                              {fields.length > 1 && (
                                <div className={styles.form_list_btn}>
                                  <DeleteOutlined
                                    style={{
                                      fontSize: '20px',
                                      cursor: 'pointer',
                                    }}
                                    onClick={() => remove(index)}
                                  />
                                </div>
                              )}
                            </div>
                          ))}
                        {fields.length > 3 && (
                          <div
                            className={styles.form_list_btn_wrap}
                            onClick={showMoreChange}
                          >
                            {showMore ? 'See Less' : 'See More'}
                            <span>
                              <DownOutlined
                                style={{
                                  transform: showMore
                                    ? 'rotate(180deg)'
                                    : 'rotate(0deg)',
                                  color: '#ef5a28',
                                  marginLeft: '10px',
                                }}
                              />
                            </span>
                          </div>
                        )}
                      </>
                    )}
                  </Form.List>
                );
              }}
            </Form.Item>
          </>
        ) : (
          <>
            <div className={styles.form_list_label}>
              <span className={styles.mark}>*</span> Hardware File{' '}
            </div>
            <div className={styles.form_upload_wrap}>
              <Form.Item
                name="file"
                noStyle
                valuePropName="fileList"
                getValueFromEvent={(e) => e.fileList}
                rules={[{ required: true, message: 'Please upload file!' }]}
              >
                <Upload {...uploadProps}>
                  <Button type="primary">Upload</Button>
                </Upload>
              </Form.Item>
            </div>
            <Form.Item
              name="expireDate"
              label="Expiration date"
              rules={[
                {
                  required: true,
                  message: 'Please select expiration date',
                },
              ]}
            >
              <Select placeholder="Select expiration date">
                {expirationDate.map((item: string) => (
                  <Select.Option key={item} value={item}>
                    {item}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </>
        )}
        <p className={styles.warning_text}>
          {softType !== 2 ? 'This is the service end date.' : ''} Please use the
          dropdown to check the support expiration dates available and select
          the correct one from the list;once the license is assigned , this
          selection cannot be changed!
        </p>
        <Form.Item wrapperCol={{ span: 12, offset: 6 }}>
          <Button
            type="primary"
            htmlType="submit"
            style={{ width: '100%' }}
            loading={loading}
          >
            Add AmpCon License
          </Button>
        </Form.Item>
      </Form>
    </FormCom>
  );
}

export default index;

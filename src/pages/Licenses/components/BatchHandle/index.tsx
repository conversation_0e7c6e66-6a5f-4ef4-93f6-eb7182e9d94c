import React, { useEffect, useRef, useState } from 'react';
import styles from './index.module.scss';
import { DownloadOutlined, UploadOutlined } from '@ant-design/icons';
import {
  Button,
  Dropdown,
  Upload,
  UploadProps,
  Modal,
  message,
  Spin,
  Form,
  Select,
  DatePicker,
} from 'antd';
import {
  downloadExcelTemplate,
  downloadLicenseExcel,
  getSpeedType,
  uploadLicenseExcel,
} from '@/api/license';
import { downloadByBlob } from '@/utils/utils';

type ErrorDataItem = {
  errorMsg: string[];
  index: number;
};

type SpeedTypeItem = {
  id: number;
  name: string;
};

function BatchHandle() {
  const [spinning, setSpinning] = useState(false);
  const [btnLoading, setBtnLoading] = useState(false);
  const [resultModalVisible, setResultModalVisible] = useState(false);
  const [downloadModalVisible, setDownloadModalVisible] = useState(false);
  const [errorData, setErrorData] = useState<ErrorDataItem[]>([]);
  const [form] = Form.useForm();
  const [speedTypeOptions, setSpeedTypeOptions] = useState<SpeedTypeItem[]>([]);

  const props: UploadProps = {
    showUploadList: false,
    accept: '.xlsx, .xls',
    maxCount: 1,
    multiple: false,
    customRequest: async ({ file }) => {
      setSpinning(true);
      const formData = new FormData();
      formData.append('file', file);
      const res = await uploadLicenseExcel(formData).finally(() =>
        setSpinning(false)
      );
      if (!res.data.isSuccess) {
        if (res.data.errorList.length) {
          setResultModalVisible(true);
          setErrorData(res.data.errorList);
        } else {
          message.error(res.data.message);
        }
      } else {
        message.success('Upload successfully');
      }
    },
  };
  const uploadMenuItem = [
    {
      key: '',
      label: (
        <Upload {...props}>
          <span>New Switch License</span>
        </Upload>
      ),
    },
  ];

  const downloadBlankTemplate = async () => {
    const data = await downloadExcelTemplate();
    downloadByBlob(data);
  };
  const downloadMenuItem = [
    {
      key: '1',
      label: <a onClick={downloadBlankTemplate}>Blank Template</a>,
    },
    {
      key: '2',
      label: (
        <a onClick={() => setDownloadModalVisible(true)}>Download Licenses</a>
      ),
    },
  ];

  const formSubmit = async () => {
    const formData = form.getFieldsValue();
    const params = {} as any;
    Object.entries(formData).forEach(([key, value]) => {
      if (value) {
        if (key === 'expireDate' || key === 'createDate') {
          if (value instanceof Array && value.length) {
            params[key] = value.map((item: any) => item.format('YYYY-MM-DD'));
          }
        } else {
          params[key] = value;
        }
      }
    });
    setBtnLoading(true);
    const data = await downloadLicenseExcel(params).finally(() =>
      setBtnLoading(false)
    );
    downloadByBlob(data);
  };

  const fetchSpeedType = async () => {
    const data = await getSpeedType();
    setSpeedTypeOptions(data.data);
  };
  useEffect(() => {
    fetchSpeedType();
  }, [resultModalVisible]);
  return (
    <>
      <Spin spinning={spinning} fullscreen />
      <div className={styles.batch_wrap}>
        <Dropdown menu={{ items: uploadMenuItem }} placement="bottomLeft">
          <Button icon={<UploadOutlined />} type="primary">
            Upload
          </Button>
        </Dropdown>
        <Dropdown menu={{ items: downloadMenuItem }} placement="bottomLeft">
          <Button icon={<DownloadOutlined />} type="primary">
            Download
          </Button>
        </Dropdown>
      </div>
      <Modal
        title="Upload Failed"
        open={resultModalVisible}
        centered
        maskClosable={false}
        onCancel={() => setResultModalVisible(false)}
        onOk={() => setResultModalVisible(false)}
      >
        <div>
          <h4>
            There are erroneous data entries. Please review and re-upload the
            file.
          </h4>
          <section className={styles.error_list_wrap}>
            <ul>
              {errorData.map((item, index) => (
                <li key={index}>
                  <div className={styles.dot}></div>
                  <div>
                    {item.errorMsg.map((e, i) => (
                      <p key={i}>{e}</p>
                    ))}
                  </div>
                </li>
              ))}
            </ul>
          </section>
        </div>
      </Modal>
      <Modal
        title="Download Licenses"
        open={downloadModalVisible}
        maskClosable={false}
        onCancel={() => setDownloadModalVisible(false)}
        okText="Download"
        centered
        onOk={formSubmit}
        confirmLoading={btnLoading}
      >
        <div>
          <p>
            Please select the data you wish to download. If none are selected,
            all data will be downloaded by default.
          </p>
          <Form form={form}>
            <Form.Item label="Speed Type" name={'speedTypeId'}>
              <Select placeholder="Please select">
                {speedTypeOptions.map((item) => (
                  <Select.Option key={item.id}>{item.name}</Select.Option>
                ))}
              </Select>
            </Form.Item>
            <Form.Item label="Expire Date" name={'expireDate'}>
              <DatePicker.RangePicker />
            </Form.Item>
            <Form.Item label="Create Date" name={'createDate'}>
              <DatePicker.RangePicker />
            </Form.Item>
          </Form>
        </div>
      </Modal>
    </>
  );
}

export default BatchHandle;

import React, { useEffect, useRef } from 'react';
import styles from './index.module.scss';
import { useLocation, useParams } from 'react-router-dom';
import { pathMap } from '@/utils/pathMap';
import { Modal, Spin } from 'antd';
import { useQuery } from 'react-query';
import { getHistoryList } from '@/api/admin-license';
import { HistoryData } from '@/api/interface';
import MinTable from './Table';
type FormComProps = {
  children?: React.ReactNode;
  pageTitle: string;
};

function FormCom(props: FormComProps) {
  const { children, pageTitle } = props;
  const location = useLocation();
  const { id } = useParams();
  const [visible, setVisible] = React.useState(false);
  const filterId = useRef('');
  const { data, isLoading } = useQuery(
    'history',
    () =>
      getHistoryList({
        contentTypeId: filterId.current,
        objectId: id || '',
      }),
    {
      enabled: visible,
      select: (res) => res.data as HistoryData[],
    }
  );
  const openHistory = () => {
    setVisible(true);
  };
  useEffect(() => {
    for (const [k, v] of Object.entries(pathMap)) {
      if (location.pathname.includes(v)) {
        filterId.current = k;
        break;
      }
    }
  }, [location.pathname]);
  return (
    <div className={styles.form_page_wrap}>
      <div className={styles.title}>{pageTitle}</div>
      {location.pathname.includes('admin') &&
        !location.pathname.includes('add') && (
          <div className={styles.history_btn} onClick={openHistory}>
            History
          </div>
        )}
      <div className={styles.form_wrap}>{children}</div>
      <Modal
        open={visible}
        title="History"
        onCancel={() => setVisible(false)}
        onOk={() => setVisible(false)}
        width={'60vw'}
      >
        <div>{isLoading ? <Spin /> : <MinTable data={data || []} />}</div>
      </Modal>
    </div>
  );
}

export default FormCom;

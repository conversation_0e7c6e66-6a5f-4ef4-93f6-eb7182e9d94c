import { HistoryData } from '@/api/interface';
import styles from './index.module.scss';
import { Empty } from 'antd';
type MinTableProps = {
  data: HistoryData[];
};

function MinTable(props: MinTableProps) {
  const { data } = props;
  return (
    <div className={styles.modal_content}>
      {data.length ? (
        <table>
          <thead>
            <tr>
              <th>Date/time</th>
              <th>User</th>
              <th>Action</th>
            </tr>
          </thead>
          <tbody>
            {data.map((item) => (
              <tr key={item.id}>
                <td>{item.actionTime}</td>
                <td>{item.userName}</td>
                <td>{item.changeMessage}</td>
              </tr>
            ))}
          </tbody>
        </table>
      ) : (
        <Empty />
      )}
    </div>
  );
}

export default MinTable;

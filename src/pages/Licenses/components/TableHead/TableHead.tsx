import { Link } from 'react-router-dom';
import styles from './index.module.scss';
import { Switch } from 'antd';
import BatchHandle from '../BatchHandle';
import { useAppSelector } from '@/store/hooks';
import { selectUserInfo } from '@/store/feature/userSlice';
type TableHeadProps = {
  onExpandAll?: (checked: boolean) => void;
  navList: NavItem[];
};
type NavItem = {
  name: string;
  path: string;
};
function TableHead(props: TableHeadProps) {
  const isScatteredCustomer =
    useAppSelector(selectUserInfo).isScatteredCustomer;

  const { onExpandAll, navList } = props;
  const handleChange = (checked: boolean) => {
    onExpandAll && onExpandAll(checked);
  };
  return (
    <div className={styles.table_head}>
      {!isScatteredCustomer && (
        <div className={styles.nav_wrap}>
          {navList.length &&
            navList.map((item, index) => (
              <Link to={item.path} key={index}>
                {item.name}
              </Link>
            ))}
        </div>
      )}
      {onExpandAll && (
        <div className={styles.switch_wrap}>
          {!isScatteredCustomer && <BatchHandle />}
          <Switch
            checkedChildren="Expand All"
            unCheckedChildren="Collapse All"
            onChange={handleChange}
          />
        </div>
      )}
    </div>
  );
}

export default TableHead;

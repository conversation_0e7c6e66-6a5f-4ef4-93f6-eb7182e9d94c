import React, { useState } from 'react';
import FormCom from '../components/FormCom';
import {
  Button,
  Form,
  Input,
  Radio,
  Select,
  Upload,
  UploadProps,
  message,
} from 'antd';
import styles from '@/pages/Admin/Ampcon/Licenses/Detail/index.module.scss';
import { downloadByBlob } from '@/utils/utils';
import {
  DeleteOutlined,
  DownOutlined,
  PlusCircleFilled,
} from '@ant-design/icons';
import {
  downloadRevokeTemplate,
  getRevokeCodeFilterItems,
  saveRevokeCode,
} from '@/api/ampcon';
import { useQuery } from 'react-query';
import { useNavigate } from 'react-router-dom';

function index() {
  const [form] = Form.useForm();
  const router = useNavigate();

  const { data: filterOptions } = useQuery(
    'revoke-code-filter-options',
    () => getRevokeCodeFilterItems(),
    {
      select: (data) => data.data,
    }
  );
  console.log(filterOptions);

  // 展开更多
  const [showMore, setShowMore] = useState(false);
  const showMoreChange = () => {
    setShowMore((prevShowMore) => !prevShowMore);
  };

  // 上传文件配置
  const uploadProps: UploadProps = {
    accept: '.xlsx, .xls',
    maxCount: 1,
    multiple: false,
    beforeUpload: (file) => {
      if (file.size > 1024 * 1024 * 2) {
        message.error('File must be smaller than 2MB!');
      }
      return false;
    },
  };
  const [downloading, setDownloading] = useState(false);

  // 下载空白模板
  const downloadBlankTemplate = async () => {
    setDownloading(true);
    const res = await downloadRevokeTemplate().finally(() =>
      setDownloading(false)
    );
    downloadByBlob(res);
  };

  const handleValuesChange = (changedValues: any, allValues: any) => {
    console.log('changedValues: ', changedValues);
    console.log('allValues: ', allValues);
  };

  const [loading, setLoading] = useState(false);
  const handleSubmit = async (values: any) => {
    console.log(values);
    setLoading(true);
    const formData = new FormData();
    Object.keys(values).forEach((key) => {
      if (key === 'file' && Array.isArray(values.file)) {
        return formData.append('file', values.file[0].originFileObj);
      }
      if (key === 'revokeCode' && Array.isArray(values.revokeCode)) {
        return values.revokeCode.forEach((item: any, index: number) => {
          formData.append(`revokeCode[${index}]`, item);
        });
      }
      formData.append(key, values[key]);
    });
    formData.append('pattern', 'Ampcon');
    const res = await saveRevokeCode(formData).finally(() => setLoading(false));
    console.log(res);
    if (res.code === 200) {
      message.success('Save success', 1.5).then(() => router(-1));
    }
  };

  return (
    <FormCom pageTitle="Verify revoke code">
      <Form
        form={form}
        size="large"
        style={{ width: '760px' }}
        layout="vertical"
        onValuesChange={handleValuesChange}
        onFinish={handleSubmit}
        initialValues={{
          softwareType: undefined,
          hasFile: false,
          revokeCode: [''],
        }}
      >
        <Form.Item
          name="softwareType"
          label="Software Type"
          rules={[{ required: true, message: 'Please select software type' }]}
        >
          <Select>
            {filterOptions?.map((item: any) => (
              <Select.Option key={item.id} value={item.id}>
                {item.name}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item label="Addition Method" name="hasFile" required>
          <Radio.Group>
            <Radio value={false}>Form input</Radio>
            <Radio value={true}>File upload</Radio>
          </Radio.Group>
        </Form.Item>
        <Form.Item noStyle shouldUpdate>
          {() => {
            const hasFile = form.getFieldValue('hasFile');
            return hasFile ? (
              <>
                <div className={styles.form_list_label}>
                  <span className={styles.mark}>*</span> Revoke Code
                </div>
                <div className={styles.form_upload_wrap}>
                  <Form.Item
                    name="file"
                    noStyle
                    valuePropName="fileList"
                    getValueFromEvent={(e) => e.fileList}
                  >
                    <Upload {...uploadProps}>
                      <Button type="primary">Upload</Button>
                    </Upload>
                  </Form.Item>
                  <Button
                    type="primary"
                    ghost
                    onClick={downloadBlankTemplate}
                    loading={downloading}
                  >
                    Blank template
                  </Button>
                </div>
              </>
            ) : (
              <Form.List name="revokeCode">
                {(fields, { add, remove }) => (
                  <>
                    <div className={styles.form_list_label}>
                      <span className={styles.mark}>*</span>Revoke Code{' '}
                      <PlusCircleFilled
                        onClick={() => {
                          add('', 0);
                        }}
                        style={{
                          cursor: 'pointer',
                          color: '#ef5a28',
                          marginLeft: '16px',
                        }}
                      />
                    </div>
                    {fields
                      .slice(0, showMore ? fields.length : 3)
                      .map((field, index) => (
                        <div className={styles.form_list_wrap} key={index}>
                          <Form.Item
                            name={[field.name]}
                            rules={[
                              {
                                required: true,
                                message: 'Please input',
                              },
                            ]}
                          >
                            <Input placeholder="Please enter device revoke code" />
                          </Form.Item>
                          {index > 0 && (
                            <div
                              className={styles.form_list_btn}
                              style={{ top: '15%' }}
                            >
                              <DeleteOutlined
                                style={{
                                  fontSize: '20px',
                                  cursor: 'pointer',
                                }}
                                onClick={() => remove(index)}
                              />
                            </div>
                          )}
                        </div>
                      ))}
                    {fields.length > 3 && (
                      <div
                        className={styles.form_list_btn_wrap}
                        onClick={showMoreChange}
                      >
                        {showMore ? 'Show Less' : 'Show More'}
                        <span>
                          <DownOutlined
                            style={{
                              transform: showMore
                                ? 'rotate(180deg)'
                                : 'rotate(0deg)',
                              color: '#ef5a28',
                              marginLeft: '10px',
                            }}
                          />
                        </span>
                      </div>
                    )}
                  </>
                )}
              </Form.List>
            );
          }}
        </Form.Item>
        <Form.Item>
          <Button type="primary" htmlType="submit" loading={loading}>
            Save
          </Button>
        </Form.Item>
      </Form>
    </FormCom>
  );
}

export default index;

import { Button, Form, Input, message } from 'antd';
import styles from './Index.module.scss';
import { postAddToken } from '@/api/license';
import FormCom from '../components/FormCom';
import { useNavigate } from 'react-router-dom';
function AddToken() {
  const router = useNavigate();
  const onFinish = async ({ comments }: { comments: string }) => {
    const data = await postAddToken({ comments });
    if (data) {
      message.success('Add token success', 1).then(() => {
        router(-1);
      });
    }
  };
  return (
    <FormCom pageTitle="Token add form">
      <Form size="large" style={{ width: 400 }} onFinish={onFinish}>
        <Form.Item label="Comments" name="comments">
          <Input />
        </Form.Item>
        <Form.Item wrapperCol={{ span: 12, offset: 6 }}>
          <Button
            type="primary"
            htmlType="submit"
            className={styles.submit_btn}
            style={{ width: '100%' }}
          >
            Add
          </Button>
        </Form.Item>
      </Form>
    </FormCom>
  );
}

export default AddToken;

import Table from '@/components/Table';
import styles from './index.module.scss';
import ExpandedRowTemplate from './ExpandRow';
import { useQuery } from 'react-query';
import { getLicenseList, getPoolLicenseList } from '@/api/license';
import React, { useEffect, useMemo, useState } from 'react';
import TableHead from '../components/TableHead/TableHead';
import { Divider, Spin } from 'antd';
import EditableCell from './EditableCell';
import { LicensesData, LicensesDataItem, PoolLicenseList } from '../interface';
import { useAppSelector } from '@/store/hooks';
import { selectUserInfo } from '@/store/feature/userSlice';

const navList = [
  {
    name: 'New Switch License',
    path: '/addLicense',
  },
  {
    name: 'New AmpCon Pool License',
    path: '/addPoolLicense',
  },
  {
    name: 'AmpCon Licenses',
    path: '/licenses/AmpconLicenses',
  },
  {
    name: 'API Token Administration',
    path: '/licenses/token',
  },
  {
    name: 'License Summary',
    path: '/licenses/summary',
  },
  {
    name: 'Password encryption',
    path: '/passwordEncryption',
  },
];
function LcContent() {
  const isScatteredCustomer =
    useAppSelector(selectUserInfo).isScatteredCustomer;

  const { data, isLoading, refetch } = useQuery(
    'licensesList',
    () => getLicenseList(),
    {
      // enabled: false,
      select: (res) => res.data as LicensesData[],
    }
  );
  const { data: poolData, isLoading: poolLoading } = useQuery(
    'licensesPoolList',
    () => getPoolLicenseList(),
    {
      select: (res) => res.data as PoolLicenseList,
      enabled: !isScatteredCustomer,
    }
  );
  console.log('poolData', poolData);
  const [expandedRowKeys, setExpandedRowKeys] = useState<React.Key[]>([]);
  const [poolExpandedRowKeys, setPoolExpandedRowKeys] = useState<React.Key[]>(
    []
  );
  const handleSave = (record: LicensesDataItem) => {
    refetch();
  };
  const renderColumns = (
    record: LicensesData | PoolLicenseList | undefined,
    type = 'license'
  ) => {
    if (record === undefined) return [];
    const { speedType, featureType, mode, remain } = record as LicensesData;
    return [
      {
        title:
          type === 'license'
            ? `${speedType} ${featureType} ${mode} license ${
                !isScatteredCustomer ? `remaining: ${remain}` : ''
              } `
            : `Pool license remaining: ${remain}`,
        dataIndex: 'featureType',
        key: 'featureType',
        width: '60%',
        render: (_: any, record: any) =>
          `${record.speedType}/${record.featureType} (${record.mode})`,
      },
      {
        width: '25%',
        title: 'Name',
        dataIndex: 'name',
        key: 'name',
        render: (text: string, record: any) =>
          type === 'license' ? (
            <EditableCell
              record={record}
              editable={true}
              handleSave={handleSave}
            />
          ) : (
            text
          ),
      },
      {
        width: '15%',
        title: 'Support Expires',
        dataIndex: 'expirationDate',
        key: 'expirationDate',
      },
    ];
  };
  const onExpandAll = (checked: boolean) => {
    if (checked) {
      const expandedRowKeys: React.Key[] = [];
      data?.forEach((item) => {
        item.licenseDataList.forEach((license) => {
          expandedRowKeys.push(license.id);
        });
      });
      setExpandedRowKeys(expandedRowKeys);
      setPoolExpandedRowKeys(
        poolData?.licenseList.map((item) => item.id) || []
      );
    } else {
      setExpandedRowKeys([]);
      setPoolExpandedRowKeys([]);
    }
  };
  const onRefresh = (status: boolean) => {
    if (status) {
      // 表格某一行刷新重新获取数据成功，如何重新渲染表格
      refetch();
    }
  };

  const isListEmpty = useMemo(() => {
    if (data) {
      return (
        (data.length === 0 || data[0]?.licenseDataList?.length === 0) &&
        poolData?.licenseList?.length === 0
      );
    }
  }, [data?.length, poolData?.licenseList]);
  return (
    <div className={styles.licenses_content}>
      <h1 className={styles.title}>Licenses</h1>
      {isListEmpty && (
        <p className={styles.no_data_tips}>
          By clicking on Download or Copy for any licensed software, I agree to
          the terms and conditions of the{' '}
          <a
            href="https://www.pica8.com/support/warranty-and-agreements/"
            target="_blank"
            rel="noopener noreferrer"
          >
            Pica8 End User License Agreement(EULA).
          </a>
        </p>
      )}
      <main className={styles.list_wrap}>
        {isLoading && (
          <div className={styles.loading_box}>
            <Spin size="large" />
          </div>
        )}
        {data && data.length > 0 ? (
          data.map((item, index) => (
            <Table
              columns={renderColumns(item)}
              dataSource={item.licenseDataList}
              pagination={false}
              expandable={{
                expandedRowKeys,
                expandedRowRender: (record) => (
                  <ExpandedRowTemplate record={record} onRefresh={onRefresh} />
                ),
                onExpandedRowsChange: (expandedRows) => {
                  setExpandedRowKeys(Array.from(expandedRows));
                },
              }}
              key={index}
              index={index}
              title={
                index === 0
                  ? () => (
                      <TableHead onExpandAll={onExpandAll} navList={navList} />
                    )
                  : undefined
              }
              customizeRenderEmpty={() => <div></div>}
            ></Table>
          ))
        ) : (
          <Table
            columns={[]}
            dataSource={[]}
            pagination={false}
            title={() => (
              <TableHead onExpandAll={onExpandAll} navList={navList} />
            )}
            customizeRenderEmpty={() => <div></div>}
          ></Table>
        )}
        {!isScatteredCustomer && (
          <Table
            columns={renderColumns(poolData, 'pool')}
            dataSource={poolData?.licenseList || []}
            pagination={false}
            expandable={{
              expandedRowKeys: poolExpandedRowKeys,
              expandedRowRender: (record) => (
                <ExpandedRowTemplate
                  record={record}
                  poolData={{
                    type: 'pool',
                    companyName: poolData?.companyName,
                  }}
                  onRefresh={onRefresh}
                />
              ),
              onExpandedRowsChange: (expandedRows) => {
                console.log(expandedRows);
                setPoolExpandedRowKeys(Array.from(expandedRows));
              },
            }}
            customizeRenderEmpty={() => <div></div>}
          ></Table>
        )}
      </main>
    </div>
  );
}

export default LcContent;

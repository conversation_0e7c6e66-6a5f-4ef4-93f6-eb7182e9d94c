import { putLicenseName } from '@/api/license';
import { EditOutlined } from '@ant-design/icons';
import { Input, InputRef, message } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { LicensesDataItem } from '../interface';

interface EditableCellProps {
  editable: boolean;
  record: LicensesDataItem;
  handleSave: (record: LicensesDataItem) => void;
}

function EditableCell({ editable, record, handleSave }: EditableCellProps) {
  const [editing, setEditing] = useState(false);
  const inputRef = useRef<InputRef>(null);
  const [value, setValue] = useState(record.name);
  const prevValue = useRef(value);
  useEffect(() => {
    if (editing) {
      inputRef.current && inputRef.current.focus();
    }
  }, [editing]);

  const toggleEdit = () => {
    setEditing(!editing);
  };

  const save = async () => {
    try {
      if (value === prevValue.current) {
        toggleEdit();
        return;
      }
      const data = await putLicenseName({ name: value, id: record.id });
      if (data.data) {
        message.success('Update Success', 2);
        toggleEdit();
        handleSave({ ...record, name: value });
      }
    } catch (errInfo) {
      console.log('Save failed:', errInfo);
    }
  };

  let childNode = <td></td>;

  if (editable) {
    childNode = editing ? (
      <Input
        ref={inputRef}
        onPressEnter={save}
        onBlur={save}
        onChange={(e) => setValue(e.target.value)}
        style={{ width: '150px' }}
        value={value}
      />
    ) : (
      <div
        className="editable-cell-value-wrap"
        style={{ paddingRight: 24, cursor: 'pointer' }}
        onClick={toggleEdit}
      >
        {record.name}
        <span>
          <EditOutlined style={{ color: '#ef5a28', marginLeft: '8px' }} />
        </span>
      </div>
    );
  }

  return childNode;
}
export default EditableCell;

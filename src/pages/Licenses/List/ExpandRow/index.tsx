import { message } from 'antd';
import styles from '../index.module.scss';
import {
  CopyOutlined,
  RedoOutlined,
  VerticalAlignBottomOutlined,
} from '@ant-design/icons';
import { putLicenseKey } from '@/api/license';

type ExpandedRowTemplateProps = {
  record: any;
  onRefresh: (status: boolean) => void;
  poolData?: {
    type: 'pool';
    companyName: string | undefined;
  };
};

function expandedRowTemplate(props: ExpandedRowTemplateProps) {
  const { record, onRefresh, poolData } = props;
  const columns = [
    {
      label: 'Mode:',
      value: record.mode,
    },
    {
      label: 'Type:',
      value: record.speedType,
    },
    {
      label: 'Feature Type:',
      value: record.featureType,
    },
    {
      label: record.mode === 'switch' ? 'Hardware ID:' : 'Site Name:',
      value: record.mode === 'switch' ? record.hardwareId : record.siteName,
    },
    {
      label: 'Expire Date:',
      value: record.expirationDate,
    },
    {
      label: 'Create Date:',
      value: record.createTime,
    },
    {
      label: 'Serial Number:',
      value: record.serialNumber,
    },
  ];
  const copyText = async () => {
    try {
      await navigator.clipboard.writeText(record.licenseKey);
      message.success('Copy Success', 2);
    } catch (err) {
      console.error('Copy failed', err);
      message.error('Copy Failed', 2);
    }
  };
  // 下载文本为lic格式
  const downloadText = () => {
    const element = document.createElement('a');
    const file = new Blob([record.licenseKey], {
      type: 'text/plain;charset=utf-8',
    });
    element.href = URL.createObjectURL(file);
    element.download = `${record.name || record.id}.lic`;
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
  };
  // 刷新课licenseKey
  const refreshLicenseKey = async () => {
    const data = await putLicenseKey(record.id);
    if (data.data.result) {
      onRefresh(true);
      message.success('Refresh Success', 2);
    }
  };

  return (
    <div className={styles.expanded_wrap}>
      <ul className={styles.list_wrap}>
        {columns.map((v) => {
          return (
            <li key={v.label} className={styles.row_wrap}>
              <span className={styles.label}>{v.label}</span>
              <span className={styles.value}>{v.value}</span>
            </li>
          );
        })}
      </ul>
      {/* <div className={styles.left_wrap}>
      </div> */}
      <div className={styles.right_wrap}>
        <div className={styles.right_item}>
          <span>License to</span>
          <span>{record.displayCompanyName || poolData?.companyName}</span>
        </div>
        <div className={styles.right_item}>
          <span>License Key</span>
          <textarea
            className={styles.key}
            value={record.licenseKey}
            cols={30}
            rows={4}
            disabled
          />
        </div>
        <div className={styles['right_item']}>
          {!poolData && (
            <div className={styles.btn_option} onClick={refreshLicenseKey}>
              <RedoOutlined />
              <span>Refresh</span>
            </div>
          )}
          <div className={styles.btn_option} onClick={downloadText}>
            <VerticalAlignBottomOutlined />
            <span>Download</span>
          </div>
          <div className={styles.btn_option} onClick={copyText}>
            <CopyOutlined />
            <span>Copy</span>
          </div>
        </div>
      </div>
    </div>
  );
}
export default expandedRowTemplate;

.licenses_content {
  width: 80%;
  padding-bottom: 50px;
  .title {
    padding: 50px 0;
    color: $fontColorGrey;
    font-size: 35px;
    text-align: center;
  }
  .loading_box {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: inherit;
  }
  .no_data_tips {
    max-width: 700px;
    padding-bottom: 10px;
    margin: 0 auto;
    color: #000;
    text-align: center;
    font-size: 16px;
    font-weight: bold;
  }
}
.expanded_wrap {
  padding: 4px 16px;
  display: flex;
  justify-content: space-between;
  .list_wrap {
    margin: 0;
    padding: 0;
    .row_wrap {
      display: flex;
      align-items: center;
      width: fit-content;
      font-size: 10px;
      width: 250px;
      margin-bottom: 8px;
      &:last-child {
        margin-bottom: 0;
      }
      .label {
        width: 30%;
        color: $fontColorGrey;
      }
      .value {
        flex: 1;
        min-height: 23px;
        padding: 4px 8px;
        background-color: #ebebeb;
        border-radius: 10px;
      }
    }
  }
  .right_wrap {
    width: 250px;
    .right_item {
      display: flex;
      align-items: flex-start;
      font-size: 10px;
      flex-wrap: wrap;
      margin-bottom: 8px;
      span {
        color: $colorPrimary;
        &:first-child {
          width: 30%;
          color: $fontColorGrey;
        }
      }
      .key {
        width: 250px;
        overflow: hidden;
      }
      .btn_option {
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 12px;
        margin-right: 12px;
      }
    }
  }
}

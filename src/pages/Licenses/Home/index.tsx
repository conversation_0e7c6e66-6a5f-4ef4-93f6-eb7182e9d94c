// import { useEffect } from 'react';
import { Outlet } from 'react-router-dom';
import styles from './index.module.scss';
import { useQuery } from 'react-query';
import { getLicenseCount } from '@/api/license';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { selectUserInfo, setCompanyCount } from '@/store/feature/userSlice';

function LicensesPage() {
  const isScatteredCustomer =
    useAppSelector(selectUserInfo).isScatteredCustomer;
  const dispatch = useAppDispatch();
  useQuery('companyCount', () => getLicenseCount(), {
    enabled: !isScatteredCustomer,
    select: (res) => res.data,
    onSuccess: (res) => {
      dispatch(setCompanyCount(res));
    },
  });

  return (
    <>
      <div className={styles.page_wrap}>
        <header className={styles.licenses_head}>
          <div className={styles.desc}>
            For questions relating to managing your account, licensing,
            purchasing or downloading our product,{' '}
            <a href="mailto:<EMAIL>">
              Please contact a customer service representative
            </a>
            . To request technical support, please visit our{' '}
            <a href="http://www.pica8.com/support" target="_parent">
              Support Portal
            </a>
            .<div className={styles['border_b']}></div>
          </div>
        </header>
        <Outlet />
        {/* <Content /> */}
      </div>
    </>
  );
}

export default LicensesPage;

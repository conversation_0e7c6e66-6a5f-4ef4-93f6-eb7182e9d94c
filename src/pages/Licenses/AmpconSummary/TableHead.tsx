import React, { useEffect, useState } from 'react';
import styles from '../AmpconLicenses/index.module.scss';
import { DatePicker, Divider, Form, Input, Select, Table } from 'antd';
import { Link } from 'react-router-dom';

type TableHeadProps = {
  onValuesChange: (changedValues: any, allValues: any) => void;
  data: OptionsType | undefined;
};
export type OptionsType = {
  companyList: CompanyList[];
  filterItems: FilterItems;
  filterList: FilterList[];
  licenseTypeList: {
    id: number;
    name: string;
  }[];
};
export interface FilterList {
  fieldName: string;
  id: number;
  list: List[];
  name: string;
  parentId: number;
}
export interface List {
  children: Children[];
  fieldName: string;
}

export interface Children {
  fieldName: string;
  id: number;
  list: any[];
  name: string;
  parentId: number;
}
interface CompanyList {
  id: number;
  name: string;
}

interface FilterItems {
  deviceType: DefaultType[];
  featureType: DefaultType[];
  softwareType: DefaultType[];
  softwareVersion: DefaultType[];
}
interface DefaultType {
  code: string;
  dictId: number;
  field: string;
  id: number;
  name: string;
  parentId: number;
}

function TableHead(props: TableHeadProps) {
  const [form] = Form.useForm();
  const softType = Form.useWatch('softwareType', form);
  const { data } = props;
  const navList = [
    {
      name: 'View AmpCon Licenses',
      path: '/licenses/ampconLicenses',
    },
  ];
  const [softVersionOptions, setSoftVersionOptions] = useState([]);
  const [deviceTypeOptions, setDeviceTypeOptions] = useState([]);
  const [featureTypeOptions, setFeatureTypeOptions] = useState([]);
  console.log(data);
  useEffect(() => {
    console.log('softType change', softType);
    setSoftVersionOptions([]);
    setDeviceTypeOptions([]);
    setFeatureTypeOptions([]);
    form.setFieldsValue({
      softwareVersion: undefined,
      deviceType: undefined,
      featureType: undefined,
    });
    if (data && softType) {
      const list =
        data?.filterList.find((item: any) => item.id === softType)?.list || [];
      list.forEach((item: any) => {
        if (item.fieldName === 'Software version') {
          setSoftVersionOptions(item.children);
        }
        if (item.fieldName === 'Device type') {
          setDeviceTypeOptions(item.children);
        }
        if (item.fieldName === 'Feature type') {
          setFeatureTypeOptions(item.children);
        }
      });
    }
  }, [softType, data]);
  return (
    <div className={styles.table_header}>
      <div className={styles.header_top}>
        <div className={styles.nav_wrap}>
          {navList.map((item, index) => (
            <Link to={item.path} key={index}>
              {item.name}
            </Link>
          ))}
        </div>
      </div>
      <Divider style={{ margin: '15px 0' }} />
      <div className={styles.header_bottom}>
        <Form
          form={form}
          layout="inline"
          className={styles.filter_head}
          onValuesChange={(changedValues, allValues) => {
            if ('softwareType' in changedValues) {
              allValues.softwareVersion = undefined;
              allValues.deviceType = undefined;
              allValues.featureType = undefined;
            }
            props.onValuesChange(changedValues, allValues);
          }}
        >
          {/* <Form.Item name="companyId">
            <Select placeholder="Company" style={{ width: '150px' }} allowClear>
              {data?.companyList.map((item) => (
                <Select.Option key={item.id} value={item.id}>
                  {item.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item> */}
          <Form.Item name="softwareType">
            <Select
              placeholder="Software type"
              style={{ width: '150px' }}
              allowClear
            >
              {data?.filterList.map((item) => (
                <Select.Option key={item.id} value={item.id}>
                  {item.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
          {/* <Form.Item name="softwareVersion">
            <Select
              placeholder="Software version"
              style={{ width: '150px' }}
              allowClear
            >
              {data?.filterItems.softwareVersion.map((item) => (
                <Select.Option key={item.id} value={item.id}>
                  {item.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item> */}
          <Form.Item name="deviceType">
            <Select
              placeholder="Device type"
              style={{ width: '150px' }}
              allowClear
            >
              {deviceTypeOptions.map((item: any) => (
                <Select.Option key={item.id} value={item.id}>
                  {item.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item name="featureType">
            <Select
              placeholder="Feature type"
              style={{ width: '150px' }}
              allowClear
            >
              {featureTypeOptions.map((item: any) => (
                <Select.Option key={item.id} value={item.id}>
                  {item.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item name="licenseType">
            <Select
              placeholder="License type"
              allowClear
              style={{ width: '150px' }}
            >
              {data?.licenseTypeList.map((item) => (
                <Select.Option key={item.id} value={item.id}>
                  {item.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
          {/* <Form.Item name="hardwareId">
            <Input.Search placeholder="Hardware ID" />
          </Form.Item>
          <Form.Item name="keyword">
            <Input.Search placeholder="License name" />
          </Form.Item>
          <Form.Item name="createTime">
            <DatePicker.RangePicker
              showTime
              placeholder={['Create date: Start date', 'End date']}
            />
          </Form.Item> */}
          <Form.Item name="expireTime">
            <DatePicker.RangePicker
              placeholder={['Expire date: Start date', 'End date']}
            />
          </Form.Item>
        </Form>
      </div>
    </div>
  );
}

export default TableHead;

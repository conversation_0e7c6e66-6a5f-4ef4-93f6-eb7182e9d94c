import React from 'react';
import Table from '@/components/Table';

import styles from '../AmpconLicenses/index.module.scss';
import TableHead from './TableHead';
import { useQuery } from 'react-query';
import {
  getUserAmpconLicensesListFilter,
  postUserSummaryList,
} from '@/api/ampcon';
import { ColumnProps } from 'antd/es/table';

type DataType = {
  available: number;
  comments: string;
  companyId: number;
  companyName: string;
  createTime: string;
  createdUser: string;
  deviceType: number;
  deviceTypeName: string;
  expirationDateName: string;
  expireDate: string;
  featureType: number;
  featureTypeName: string;
  id: number;
  licenseType: number;
  licenseTypeName: string;
  pattern: string;
  remaining: number;
  softwareType: number;
  softwareTypeName: string;
  softwareVersion: number;
  softwareVersionName: string;
  updateTime: string;
  updatedUser: string;
  usedQuantity: number;
};

function index() {
  const [tableParams, setTableParams] = React.useState({
    current: 1,
    size: 10,
  });
  const { data } = useQuery(
    ['summaryList', tableParams],
    () => postUserSummaryList(tableParams),
    {
      select: (res) => res.data,
    }
  );
  const { data: optionsData } = useQuery(
    'getFilter',
    () => getUserAmpconLicensesListFilter(),
    {
      select: (res) => res.data as any,
    }
  );

  const columns: ColumnProps<DataType>[] = [
    {
      title: 'Software type',
      dataIndex: 'softwareTypeName',
    },
    // {
    //   title: 'Software version',
    //   dataIndex: 'softwareVersionName',
    // },
    {
      title: 'Device type',
      dataIndex: 'deviceTypeName',
    },
    {
      title: 'Feature type',
      dataIndex: 'featureTypeName',
    },
    {
      title: 'License type',
      dataIndex: 'licenseTypeName',
    },
    {
      title: 'Assigned',
      dataIndex: 'usedQuantity',
    },
    {
      title: 'Remaining',
      dataIndex: 'remaining',
    },
    {
      title: 'Expire date',
      dataIndex: 'expirationDateName',
    },
  ];
  const onValuesChange = (changed: any, allValues: any) => {
    let expireTime = undefined;
    let createTime = undefined;
    if (Array.isArray(allValues.createTime)) {
      createTime = [
        allValues.createTime[0].format('YYYY-MM-DD HH:mm:ss'),
        allValues.createTime[1].format('YYYY-MM-DD HH:mm:ss'),
      ];
    }
    if (Array.isArray(allValues.expireTime)) {
      expireTime = [
        allValues.expireTime[0].format('YYYY-MM-DD'),
        allValues.expireTime[1].format('YYYY-MM-DD'),
      ];
    }
    setTableParams({ ...tableParams, ...allValues, expireTime, createTime });
  };
  return (
    <div className={styles.ampcon_container}>
      <h1 className={styles.title}>AmpCon License Summary</h1>
      <Table
        scroll={{ x: 1200 }}
        columns={columns}
        dataSource={data?.records || []}
        title={() => (
          <TableHead data={optionsData} onValuesChange={onValuesChange} />
        )}
        pagination={{
          size: 'small',
          total: data?.total || 0,
          current: tableParams.current,
          showSizeChanger: true,
          showTotal: (total) => `Total ${total} items`,
          onChange: (page, pageSize) => {
            setTableParams({
              ...tableParams,
              current: page,
              size: pageSize,
            });
          },
        }}
      ></Table>
    </div>
  );
}

export default index;

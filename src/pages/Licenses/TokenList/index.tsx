import React, { useEffect } from 'react';
import styles from './index.module.scss';
import { delToken, getTokenList } from '@/api/license';
import { useQuery } from 'react-query';
import Table from '@/components/Table';
import { ColumnsType } from 'antd/es/table';
import { Popconfirm, Space, message } from 'antd';
import {
  CopyOutlined,
  DeleteOutlined,
  VerticalAlignBottomOutlined,
} from '@ant-design/icons';
import classNames from 'classnames/bind';
import TableHead from '../components/TableHead/TableHead';
import { TokenListData } from '../interface';
const cx = classNames.bind(styles);

const navList = [
  {
    name: 'License Summary',
    path: '/licenses/summary',
  },
  {
    name: 'Generate New API Token',
    path: '/addToken',
  },
  {
    name: 'License View',
    path: '/licenses',
  },
];
function TokenList() {
  const { data, isLoading, refetch } = useQuery(
    'tokenList',
    () => getTokenList(),
    {
      enabled: false,
      select: (res) => res.data as TokenListData[],
    }
  );
  useEffect(() => {
    refetch();
  }, []);
  const copyToken = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      message.success('Copy Success', 2);
    } catch (err) {
      console.error('Copy failed', err);
    }
  };
  const downloadToken = (record: TokenListData) => {
    const element = document.createElement('a');
    const file = new Blob([record.token], {
      type: 'text/plain;charset=utf-8',
    });
    element.href = URL.createObjectURL(file);
    element.download = 'token.txt';
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
  };

  const delConfirm = async (id: number) => {
    const data = await delToken(id);
    if (data) {
      message.success('Delete Success', 2);
      refetch();
    }
  };
  const columns: ColumnsType<TokenListData> = [
    {
      width: 150,
      title: 'Username',
      dataIndex: 'username',
      key: 'username',
    },
    {
      title: 'Token',
      dataIndex: 'token',
      key: 'token',
      render: (text: string) => (
        <div className={styles.token_wrap}>
          <span className={styles.text}>{text}</span>
          <div className={styles.action_btn} onClick={() => copyToken(text)}>
            <CopyOutlined />
            <span className={cx(['font_orange'])}>Copy</span>
          </div>
        </div>
      ),
    },
    {
      title: 'Comments',
      dataIndex: 'comments',
      key: 'comments',
    },
    {
      title: 'Create Date',
      dataIndex: 'createTime',
      key: 'createTime',
    },
    {
      title: 'Download',
      key: 'download',
      render: (_, record) => (
        <Space size={20}>
          <div
            className={cx(['action_btn', 'font_orange'])}
            onClick={() => downloadToken(record)}
          >
            <VerticalAlignBottomOutlined />
            <span>Download</span>
          </div>
          <Popconfirm
            title="Delete the token"
            description="Are you sure to delete this token?"
            onConfirm={() => delConfirm(record.id)}
            okText="Yes"
            cancelText="No"
          >
            <div className={styles.action_btn}>
              <DeleteOutlined />
              <span>Delete</span>
            </div>
          </Popconfirm>
        </Space>
      ),
    },
  ];
  return (
    <div className={styles.token_content}>
      <h1 className={styles.title}>Tokens</h1>
      <main className={styles.table_wrap}>
        <Table
          columns={columns}
          dataSource={data || []}
          pagination={false}
          title={() => <TableHead navList={navList} />}
        ></Table>
      </main>
    </div>
  );
}

export default TokenList;

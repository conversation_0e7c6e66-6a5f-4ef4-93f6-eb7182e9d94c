import { getSummaryList } from '@/api/license';
import styles from './index.module.scss';
import { useQuery } from 'react-query';
import Table from '@/components/Table';
import { ColumnsType } from 'antd/es/table';
import TableHead from '../components/TableHead/TableHead';
import { SummaryListItemData } from '../interface';

const navList = [
  {
    name: 'License View',
    path: '/licenses',
  },
  {
    name: 'Password encryption',
    path: '/passwordEncryption',
  },
  {
    name: 'API Token Administration',
    path: '/licenses/token',
  },
];
function SummaryList() {
  const { data, isLoading } = useQuery('summaryList', () => getSummaryList(), {
    select: (res) => res.data.licenseSummaryList as SummaryListItemData[],
  });
  const columns: ColumnsType<SummaryListItemData> = [
    {
      title: 'License speed type',
      dataIndex: 'speedType',
      sorter: {
        compare: (a, b) => a.speedTypeId - b.speedTypeId,
        multiple: 5,
      },
    },
    {
      title: 'License feature type',
      dataIndex: 'featureType',
      sorter: {
        compare: (a, b) => a.featureTypeId - b.featureTypeId,
        multiple: 4,
      },
    },
    {
      title: 'Assigned',
      dataIndex: 'assigned',
      sorter: {
        compare: (a, b) => a.assigned - b.assigned,
        multiple: 3,
      },
    },
    {
      title: 'Remaining',
      dataIndex: 'remaining',
      sorter: {
        compare: (a, b) => a.remaining - b.remaining,
        multiple: 2,
      },
    },
    {
      title: 'Expire date',
      dataIndex: 'expirationDate',
      sorter: {
        compare: (a, b) =>
          new Date(a.expirationDate).getTime() -
          new Date(b.expirationDate).getTime(),
        multiple: 1,
      },
    },
  ];
  return (
    <div className={styles.summary_wrap}>
      <h1 className={styles.title}>License summary</h1>
      <main className={styles.table_wrap}>
        <Table
          dataSource={data || []}
          columns={columns}
          pagination={false}
          title={() => <TableHead navList={navList} />}
        ></Table>
      </main>
    </div>
  );
}

export default SummaryList;

import FormCom from '../components/FormCom';
import { Button, Form, Input } from 'antd';
import { postPasswordEncrypt } from '@/api/license';

function PasswordEncrypt() {
  const [form] = Form.useForm();
  const onFinish = async ({ password }: { password: string }) => {
    const data = await postPasswordEncrypt({ password });
    if (data.data) {
      form.setFieldsValue({
        encryptedPassword: data.data,
      });
    }
  };
  return (
    <FormCom pageTitle="Password encryption">
      <Form size="large" style={{ width: 400 }} onFinish={onFinish} form={form}>
        <Form.Item
          label="Password"
          name="password"
          rules={[
            {
              required: true,
              message: 'Please input your password!',
            },
          ]}
        >
          <Input />
        </Form.Item>
        <Form.Item label="Encrypted" name="encryptedPassword">
          <Input.TextArea
            disabled
            style={{ height: '120px', wordBreak: 'break-all' }}
          />
        </Form.Item>
        <Form.Item wrapperCol={{ span: 12, offset: 6 }}>
          <Button type="primary" htmlType="submit" style={{ width: '100%' }}>
            Encryption
          </Button>
        </Form.Item>
      </Form>
    </FormCom>
  );
}

export default PasswordEncrypt;

import { useRef, useState } from 'react';
import { Button, Form, Input, Select, message } from 'antd';
import styles from '../AddLicense/index.module.scss';
import { useQuery } from 'react-query';
import UrlMap from '@/api/license/urlMap';
import {
  getAddPoolFormOptions,
  postAddLicense,
  postAddPoolLicense,
} from '@/api/license';
import { AddLicenseFieldType } from '@/api/interface';
import FormCom from '../components/FormCom';
import { useNavigate } from 'react-router-dom';

function AddPoolLicense() {
  const router = useNavigate();
  const [form] = Form.useForm();
  const { data: formOptionData, isLoading: loading } = useQuery(
    UrlMap.GET_ADD_POOL_LICENSE_FORM_OPTIONS,
    () => getAddPoolFormOptions(),
    {
      select: (res) => res.data,
    }
  );
  const onFinish = async (values: AddLicenseFieldType) => {
    console.log(values);
    const params = {
      ...values,
      name: values.name || '',
      mode: 'switch',
    };
    const data = await postAddPoolLicense(params);
    if (data.data.result) {
      message.success('Add license success', 1).then(() => {
        router(-1);
      });
    } else {
      message.error(data.data.message);
    }
  };

  return (
    <FormCom pageTitle="Pool License add form">
      <Form
        size="large"
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 16 }}
        style={{ minWidth: 600 }}
        labelAlign="left"
        form={form}
        onFinish={onFinish}
      >
        <Form.Item<AddLicenseFieldType>
          label="Speed Type"
          name="speedTypeId"
          rules={[{ required: true, message: 'Please select speed type!' }]}
        >
          <Select
            placeholder="Select speed type"
            fieldNames={{ label: 'name', value: 'id' }}
            options={formOptionData?.speedTypeList}
          ></Select>
        </Form.Item>
        <Form.Item<AddLicenseFieldType>
          label="Feature Type"
          name="featureTypeId"
          rules={[{ required: true, message: 'Please select feature type' }]}
        >
          <Select
            placeholder="Select speed type"
            fieldNames={{ label: 'name', value: 'id' }}
            options={formOptionData?.featureTypeList}
          ></Select>
        </Form.Item>
        <Form.Item<AddLicenseFieldType>
          label="Hardware ID"
          name="hardwareId"
          rules={[
            {
              required: true,
              message: 'Please, provide correct format',
              pattern: /^[0-9a-fA-F]{4}(-[0-9a-fA-F]{4}){3}$/,
            },
          ]}
        >
          <Input />
        </Form.Item>
        <Form.Item<AddLicenseFieldType> label="License Name" name="name">
          <Input />
        </Form.Item>
        <Form.Item<AddLicenseFieldType>
          label="Expiration Date"
          name="expirationDate"
          rules={[{ required: true, message: 'Please select expiration date' }]}
        >
          <Select placeholder="Select expiration date">
            {formOptionData?.expirationDateList.map((item) => (
              <Select.Option value={item} key={item}>
                {item}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        <div className={styles.form_desc}>
          <section className={styles.top}>
            <div>
              Please use the dropdown to check the support expiration dates
              available and select the correct one from the list;
            </div>
            <div>
              once the license is assigned to a switch, this selection cannot be
              changed!
            </div>
          </section>
          <section className={styles.bottom}>
            <div>
              The "License name" is an optional field that helps you
              differentiate between your licenses.
            </div>
            <div>
              A good practice could be to use your device name in this field.
            </div>
          </section>
        </div>
        <Form.Item wrapperCol={{ span: 12, offset: 6 }}>
          <Button type="primary" htmlType="submit" style={{ width: '100%' }}>
            Add Pool License
          </Button>
        </Form.Item>
      </Form>
    </FormCom>
  );
}

export default AddPoolLicense;

.login_wrap {
  width: 100vw;
  height: 100vh;
  background-color: $bgColor;
  display: flex;
  flex-direction: column;
  .main_wrap {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 auto;
    padding-top: 30px;
    .log {
      width: 150px;
      height: 45px;
      margin-top: 30px;
    }
    .title_log {
      color: $colorPrimary;
      font-size: 35px;
      padding: 50px 0 50px 0;
    }
    .log_form {
      width: 450px;
      text-align: center;
    }
  }
  .footer_wrap {
    padding: 20px 0;
    font-size: 18px;
    color: #9c9c9c;
    background-color: #333333;
    text-align: center;
    p {
      margin: 0;
      a {
        color: $colorPrimary;
        text-decoration: none;
      }
    }
  }
}
.login_input {
  &:-webkit-autofill,
  &:-webkit-autofill:hover,
  &:-webkit-autofill:focus {
    -webkit-box-shadow: 0px 0px 0px 1000px #141414 inset !important;
    -webkit-text-fill-color: #fff;
    border-radius: 0;
  }
  input {
    &:-webkit-autofill,
    &:-webkit-autofill:hover,
    &:-webkit-autofill:focus {
      -webkit-box-shadow: 0px 0px 0px 1000px #141414 inset !important;
      -webkit-text-fill-color: #fff;
      border-radius: 0;
    }
  }
}

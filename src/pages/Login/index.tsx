import { useEffect, useState } from 'react';
import { Form, Input, message } from 'antd';
import { getTicketJwt, getUserRole, userLogin } from '@/api/user/login';
import { useAppDispatch } from '@/store/hooks';
import { setToken, setUserInfo } from '@/store/feature/userSlice';
import { useNavigate } from 'react-router-dom';
import styles from './index.module.scss';
import PcInput from '../../components/Input/index';
import PcButton from '../../components/Button/index';
import { useSearchParams } from 'react-router-dom';
import { setCookieWithToken } from '@/utils/utils';
import Cookies from 'js-cookie';

const defaultStyle: React.CSSProperties = {
  width: '83%',
  height: '49px',
  marginBottom: '20px',
  borderRadius: '20px',
  padding: '16px',
  borderColor: '#ef5a28',
};
type FieldType = {
  username: string;
  password: string;
};
function Login() {
  const dispatch = useAppDispatch();
  const router = useNavigate();
  const [loading, setLoading] = useState(false);
  const [searchParams, setSearchParams] = useSearchParams();
  const nextPath = searchParams.get('nextPath');
  const from = searchParams.get('from');
  const onFinish = async (value: FieldType) => {
    setLoading(true);
    const data = await userLogin(value).finally(() => setLoading(false));
    if (data.code === 200) {
      const { accessToken, authorities } = data.data;
      setCookieWithToken(accessToken);
      dispatch(setToken(accessToken));
      // 如果是ticket页面，登录成功后直接跳转到ticket页面
      if (nextPath && from === 'ticket') {
        handleZendeskRedirect();
        return;
      }
      if (nextPath && from === 'download') {
        if (!authorities.some((item) => item === 'download-prod')) {
          return router('/unauthorized');
        }
        window.location.href = nextPath;
        return;
      }
      message.success('Login success!');
      const roleInfo = await getUserRole();
      dispatch(setUserInfo(roleInfo.data));
      if (roleInfo.data) {
        router('/');
      }
    }
  };
  const handleZendeskRedirect = async () => {
    const data = await getTicketJwt();
    if (data.code === 200) {
      window.location.href = `https://fscomhelp.zendesk.com/access/jwt?jwt=${data.data}`;
    }
  };
  useEffect(() => {
    if (Cookies.get('picaToken')) {
      if (nextPath && from === 'ticket') {
        handleZendeskRedirect();
        return;
      }
    }
  }, []);
  return (
    <div className={styles['login_wrap']}>
      <section className={styles['main_wrap']}>
        <img
          src={
            'https://resource.fs.com/mall/generalImg/20240227153448blan6k.png'
          }
          className={styles['log']}
        />
        <h1 className={styles['title_log']}>Log in</h1>
        <div className={styles.log_form}>
          <Form
            onFinish={onFinish}
            onFinishFailed={(errorInfo) => {
              message.error('Please input your username and password!');
            }}
          >
            <Form.Item<FieldType>
              name="username"
              rules={[
                { required: true, message: 'Please input your username!' },
              ]}
            >
              <PcInput
                placeholder="User..."
                size="large"
                style={defaultStyle}
                autoComplete="on"
                rootClassName={styles.login_input}
              />
            </Form.Item>
            <Form.Item<FieldType>
              name="password"
              rules={[
                { required: true, message: 'Please input your password!' },
              ]}
            >
              <Input.Password
                style={defaultStyle}
                size="large"
                placeholder="Password"
                autoComplete="on"
                rootClassName={styles.login_input}
              />
            </Form.Item>
            <Form.Item>
              <PcButton
                size="large"
                type="primary"
                style={{ width: '80%' }}
                htmlType="submit"
                loading={loading}
              >
                Log in
              </PcButton>
            </Form.Item>
          </Form>
        </div>
      </section>
      <footer className={styles['footer_wrap']}>
        <p>
          Forgot your password? Please contact{' '}
          <a href="mailto:<EMAIL>">Pica8 support team!</a>
        </p>
      </footer>
    </div>
  );
}

export default Login;

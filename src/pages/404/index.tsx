import { Button, Result } from 'antd';
import './404.scss';
import { useNavigate } from 'react-router-dom';

function NotFound() {
  const router = useNavigate();
  return (
    <Result
      status="404"
      title="404"
      subTitle="Sorry, the page you visited does not exist."
      extra={
        <Button type="primary" onClick={() => router('/')}>
          Back Home
        </Button>
      }
      rootClassName="not_found"
    />
  );
}

export default NotFound;

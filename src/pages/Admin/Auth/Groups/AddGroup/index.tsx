import {
  addGroup,
  editGroup,
  getGroupDetail,
  getPermissionList,
} from '@/api/auth';
import Transfer from '@/components/Transfer';
import useBreadcrumb from '@/hooks/useBreadcrumb';
import useSetGlobalLoading from '@/hooks/useSetGlobalLoading';
import FormCom from '@/pages/Licenses/components/FormCom';
import { Button, Form, Input, message } from 'antd';
import { useEffect } from 'react';
import { useQuery } from 'react-query';
import { useNavigate, useParams } from 'react-router-dom';

type GroupDetailType = {
  availablePermissions: PermissionItem[];
  chosenPermissions: PermissionItem[];
  name: string;
  id: number;
};

type PermissionItem = {
  id: string;
  name: string;
};

function formatPermissions(permissions: PermissionItem[]) {
  return permissions?.map((item) => ({
    key: item.id,
    title: item.name,
  }));
}

function AddGroup() {
  const router = useNavigate();
  const { id } = useParams();
  const [form] = Form.useForm();
  const { data: permissions } = useQuery(
    ['getPermissionsList'],
    () => getPermissionList(),
    {
      select: (res) => res.data,
      enabled: !id,
    }
  );
  const { data: groupDetail, isLoading: fetchInfoLoading } = useQuery(
    ['getGroupInfo', id],
    () => getGroupDetail({ groupId: id || '' }),
    {
      enabled: !!id,
      select: (res) => res.data as GroupDetailType,
    }
  );
  useSetGlobalLoading(fetchInfoLoading);

  const onFinish = async (values: any) => {
    if (id) {
      const data = await editGroup({ ...values, id });
      if (data) {
        message.success('Edit group success', 1).then(() => {
          router(-1);
        });
      }
    } else {
      const data = await addGroup(values);
      if (data) {
        message.success('Add group success', 1).then(() => {
          router(-1);
        });
      }
    }
  };
  useEffect(() => {
    if (groupDetail) {
      form.setFieldsValue({
        name: groupDetail.name,
        permissionList: groupDetail.chosenPermissions.map((item) => item.id),
      });
    }
  }, [groupDetail]);

  return (
    <FormCom pageTitle="Groups">
      <Form layout="vertical" onFinish={onFinish} form={form}>
        <Form.Item
          label="Name"
          name="name"
          rules={[{ required: true, message: 'Please input name!' }]}
        >
          <Input></Input>
        </Form.Item>
        <Form.Item
          label="permissions"
          name="permissionList"
          rules={[{ required: true, message: 'Please select permissions!' }]}
        >
          <Transfer
            dataSource={formatPermissions(
              permissions || [
                ...(groupDetail?.availablePermissions || []),
                ...(groupDetail?.chosenPermissions || []),
              ]
            )}
          ></Transfer>
        </Form.Item>
        <Form.Item wrapperCol={{ offset: 21 }}>
          <Button
            type="primary"
            htmlType="submit"
            style={{ marginRight: '10px' }}
          >
            Save
          </Button>
        </Form.Item>
      </Form>
    </FormCom>
  );
}

export default AddGroup;

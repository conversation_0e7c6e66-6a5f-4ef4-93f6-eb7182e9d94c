import React, { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Spin, message } from 'antd';
import CommonList from '../../../components/CommonList';
import { useQuery } from 'react-query';
import { delGroups, getAuthGroupsList, getRelationList } from '@/api/auth';
import styles from './index.module.scss';
import { useNavigate } from 'react-router-dom';
import usePermission from '@/hooks/usePermission';
import useBreadcrumb from '@/hooks/useBreadcrumb';
import { Link } from 'react-router-dom';

function GroupList() {
  const router = useNavigate();
  const { hasAdd, hasDelete } = usePermission();
  const breadcrumb = useBreadcrumb();
  const [page, setPage] = useState({ size: 10, current: 1, groupName: '' });
  const [idList, setIdList] = useState<React.Key[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { data, refetch } = useQuery(
    ['getGroupsList', page],
    () => getAuthGroupsList(page),
    {
      select: (res) => res.data,
    }
  );
  const { data: relationList, isLoading: relationLoading } = useQuery(
    ['groupRelationList'],
    () => getRelationList({ idList }),
    {
      select: (res) => res.data,
      enabled: isModalOpen,
    }
  );
  const columns = [
    {
      title: 'Group',
      dataIndex: 'name',
      render: (text: string, record: any) => (
        <Link to={`${record.id}`} onClick={() => breadcrumb(record.name)}>
          {text}
        </Link>
      ),
    },
    {
      title: 'Action',
      render: (text: string, record: any) =>
        hasDelete ? <a onClick={() => delItemGroups(record)}>Delete</a> : '/',
    },
  ];
  const rowSelection = {
    onChange: (selectedRowKeys: React.Key[]) => {
      setIdList(selectedRowKeys);
    },
    selectedRowKeys: idList,
  };
  const handleSearch = async (value: string) => {
    setPage({ ...page, current: 1, groupName: value });
  };
  const handleDelSelect = async () => {
    if (relationLoading) return;
    const data = await delGroups({ idList });
    if (data) {
      message.success('Delete Success!', 2);
      setIsModalOpen(false);
      refetch();
      setIdList([]);
    }
  };
  const closeModal = () => {
    setIsModalOpen(false);
  };
  const delAllGroups = () => {
    if (idList.length > 0) {
      setIsModalOpen(true);
    } else {
      message.warning('Please select at least one item!', 2);
    }
  };
  const delItemGroups = (record: any) => {
    setIdList([record.id]);
    setIsModalOpen(true);
  };
  return (
    <>
      <CommonList
        pageTitle="Groups"
        dataSource={data?.records || []}
        columns={columns}
        showAddBtn={true}
        rowSelection={hasDelete ? { ...rowSelection } : undefined}
        handleDelSelect={delAllGroups}
        handleSearch={handleSearch}
        pagination={{
          total: data?.total,
          current: page.current,
          showTotal: (total) => `${total} Groups`,
          showSizeChanger: false,
          onChange: (current) => setPage({ ...page, current }),
        }}
      >
        {hasAdd && (
          <Button
            type="primary"
            onClick={() => router('/admin/auth/group/add')}
          >
            Add Group
          </Button>
        )}
      </CommonList>
      <Modal
        title="Delete Group"
        open={isModalOpen}
        onCancel={closeModal}
        onOk={handleDelSelect}
        style={{ paddingBottom: '16px' }}
        destroyOnClose
      >
        <div className={styles.del_modal}>
          {relationLoading ? (
            <Spin size="large" />
          ) : (
            <>
              <p>
                Are you sure you want to delete the selected groups? All of the
                following objects and their related items will be deleted:
              </p>
              <div className={styles.content_wrap}>
                {relationList &&
                  relationList.map((item: any, index: number) => (
                    <section key={index}>
                      <div>Group: {item.groupName}</div>
                      <ul>
                        {item.remarkList.map((child: any, index: number) => {
                          return <li key={index}>{child}</li>;
                        })}
                      </ul>
                    </section>
                  ))}
              </div>
            </>
          )}
        </div>
      </Modal>
    </>
  );
}

export default GroupList;

import React, { useRef, useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>rm, Spin, message } from 'antd';
import CommonList from '../../../components/CommonList';
import { useQuery } from 'react-query';
import { delCompanyPool, getCompanyPoolPage } from '@/api/client';
import styles from './index.module.scss';
import { Link, useNavigate } from 'react-router-dom';
import useBreadcrumb from '@/hooks/useBreadcrumb';

function CompaniesList() {
  const router = useNavigate();
  const breadcrumb = useBreadcrumb();
  const [page, setPage] = useState({
    size: 10,
    current: 1,
    keyword: '',
    sortList: [] as any[],
  });
  const selectedKeys = useRef<React.Key[]>([]);
  const [idList, setIdList] = useState<React.Key[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { data, refetch } = useQuery(
    ['getCompanyPoolPage', page],
    () => getCompanyPoolPage(page),
    {
      select: (res) => res.data,
    }
  );
  const columns = [
    {
      title: 'Company name',
      dataIndex: 'companyName',
      render: (text: string, record: any) => (
        <Link
          to={`/admin/client/companypool/${record.id}`}
          onClick={() => breadcrumb(record.name)}
        >
          {text}
        </Link>
      ),
      sorter: {
        multiple: 1,
      },
    },
    {
      title: 'Name',
      dataIndex: 'name',
      sorter: {
        multiple: 2,
      },
    },
    {
      title: 'License Entitlement',
      dataIndex: 'allocated',
      sorter: {
        multiple: 3,
      },
    },
    {
      title: 'Assigned licenses',
      dataIndex: 'assigned',
      sorter: {
        multiple: 4,
      },
    },
    {
      title: 'Available licenses',
      dataIndex: 'available',
    },
    {
      title: 'Switch expire date',
      dataIndex: 'switchExpireDateStr',
      sorter: {
        multiple: 5,
      },
    },
    {
      title: 'Create time',
      dataIndex: 'createTimeStr',
      sorter: {
        multiple: 6,
      },
    },
    {
      title: 'Action',
      render: (text: string, record: any) => (
        <a onClick={() => delItemGroups(record)}>Delete</a>
      ),
    },
  ];
  const rowSelection = {
    onChange: (selectedRowKeys: React.Key[]) => {
      selectedKeys.current = selectedRowKeys;
    },
  };
  const handleSearch = async (value: string) => {
    setPage({ ...page, current: 1, keyword: value });
  };
  const handleDelSelect = async () => {
    const data = await delCompanyPool({ idList });
    if (data) {
      message.success('Delete Success!', 2);
      setIsModalOpen(false);
      refetch();
    }
  };
  const closeModal = () => {
    setIsModalOpen(false);
  };
  const delAllGroups = () => {
    if (selectedKeys.current.length > 0) {
      setIdList(selectedKeys.current);
      setIsModalOpen(true);
    } else {
      message.warning('Please select at least one item!', 2);
    }
  };
  const delItemGroups = (record: any) => {
    setIdList([record.id]);
    setIsModalOpen(true);
  };

  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    const sortList = [];
    if (sorter instanceof Array) {
      sorter.forEach((item) => {
        sortList.push({
          column: item.field,
          order: item.order === 'ascend' ? 'ASC' : 'DESC',
        });
      });
    } else {
      if (sorter.order) {
        sortList.push({
          column: sorter.field,
          order: sorter.order === 'ascend' ? 'ASC' : 'DESC',
        });
      }
    }
    setPage({
      ...page,
      current: pagination.current,
      size: pagination.pageSize,
      sortList,
    });
  };
  return (
    <div>
      <CommonList
        pageTitle="Companies"
        dataSource={data?.records || []}
        columns={columns}
        showAddBtn={true}
        rowSelection={{
          ...rowSelection,
        }}
        handleDelSelect={delAllGroups}
        handleSearch={handleSearch}
        onChange={handleTableChange}
        pagination={{
          total: data?.total,
          current: page.current,
          showTotal: (total) => `${total} Companies pools`,
          // onChange: (current, pageSize) =>
          //   setPage({ ...page, current, size: pageSize }),
        }}
      >
        <Button
          type="primary"
          onClick={() => router('/admin/client/companypool/add')}
        >
          Add Company pools
        </Button>
      </CommonList>
      <Modal
        title="Delete Company pools"
        open={isModalOpen}
        onCancel={closeModal}
        onOk={handleDelSelect}
        style={{ paddingBottom: '16px' }}
        destroyOnClose
      >
        <div className={styles.del_modal}>
          <p>
            Are you sure you want to delete the selected company pool? All of
            the following objects and their related items will be deleted:
          </p>
          <div className={styles.content_wrap}>
            {idList.map((key) => {
              return (
                <p key={key}>
                  Company pool:
                  {data?.records.find((v: any) => v.id === key)?.name}
                </p>
              );
            })}
          </div>
        </div>
      </Modal>
    </div>
  );
}

export default CompaniesList;

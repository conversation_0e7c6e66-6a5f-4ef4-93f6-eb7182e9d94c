import React, { useState } from 'react';
import FormCom from '@/pages/Licenses/components/FormCom';
import { Button, Form, Input, message, Select, DatePicker } from 'antd';
import dayjs from 'dayjs';
import { PlusOutlined } from '@ant-design/icons';
import styles from './index.module.scss';
import { useNavigate, useParams } from 'react-router-dom';
import { useQuery } from 'react-query';
import {
  getCompanyEditPoolDetail,
  postCompanyPollDetailEdit,
  queryCompanyPage,
  companyPool,
} from '@/api/client';

const { TextArea } = Input;
const AddCompanyPool = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const dateFormat = 'YYYY-MM-DD';
  const [page, setPage] = useState({
    size: 10000,
    current: 1,
    companyName: '',
  });
  const [loading, setLoading] = useState<boolean>(false);

  const { data } = useQuery(
    ['queryCompanyPage', page],
    () => queryCompanyPage(page),
    {
      select: (res) => res.data?.records,
    }
  );

  const result = useQuery(
    ['getCompanyEditPoolDetail', {}],
    () => getCompanyEditPoolDetail({ companyPoolId: Number(id) }),
    {
      select: (res) => res.data,
      enabled: !!id,
      onSuccess: (data) => {
        form.setFieldsValue({
          ...data,
          switchExpireDate: dayjs(data?.switchExpireDate, dateFormat),
          ampconExpireDate: dayjs(data?.ampconExpireDate, dateFormat),
        });
      },
    }
  );
  const onFinish = (values: any) => {
    console.log(values);
    const params: companyPool = {
      ...values,
      switchExpireDate: values.switchExpireDate
        ? dayjs(values.switchExpireDate).format(dateFormat)
        : '',
      ampconExpireDate: values.ampconExpireDate
        ? dayjs(values.ampconExpireDate).format(dateFormat)
        : '',
    } as any;
    if (id) {
      Object.assign(params, { id: +id });
    }
    setLoading(true);
    try {
      postCompanyPollDetailEdit(params)
        .then((res) => {
          console.log(res);
          if (res.code === 200) {
            message.success('Success');
            navigate('/admin/client/companypool');
          } else {
            message.error(res.message);
          }
          setLoading(false);
        })
        .finally(() => {
          setLoading(false);
        });
    } catch (err) {
      console.error(err);
      setLoading(false);
    }
  };

  return (
    <div className={styles.AddCompanyPool}>
      <Form size="large" onFinish={onFinish} form={form} layout="vertical">
        <FormCom pageTitle="Companies">
          <div style={{ width: 400 }}>
            <Form.Item
              label="Company"
              name="companyId"
              rules={[{ required: true }]}
            >
              <Select
                fieldNames={{ label: 'name', value: 'id' }}
                options={data}
                showSearch
                filterOption={(input, option) =>
                  (option?.name ?? '').includes(input)
                }
              />
            </Form.Item>
            <Button
              type="primary"
              shape="circle"
              icon={<PlusOutlined />}
              onClick={() => navigate('/admin/client/company/add')}
            ></Button>
            <Form.Item label="Name" name="name" rules={[{ required: true }]}>
              <Input />
            </Form.Item>
            <Form.Item
              label="License Entitlement"
              name="allocated"
              rules={[{ required: true }]}
            >
              <Input />
            </Form.Item>
            <Form.Item
              label="Switch expire date"
              name="switchExpireDate"
              rules={[{ required: true }]}
            >
              <DatePicker style={{ width: 400 }} format={dateFormat} />
            </Form.Item>
            <Form.Item
              label="AmpCon expire date"
              name="ampconExpireDate"
              rules={[{ required: true }]}
            >
              <DatePicker style={{ width: 400 }} format={dateFormat} />
            </Form.Item>
          </div>
        </FormCom>
        <div style={{ width: 600, margin: '0 auto' }}>
          <Form.Item
            label="Comments"
            name="comments"
            rules={[{ required: true }]}
          >
            <TextArea
              className={styles.textarea}
              autoSize={{ minRows: 10, maxRows: 10 }}
            />
          </Form.Item>
        </div>
        <Form.Item wrapperCol={{ offset: 4, span: 16 }}>
          <Button
            type="primary"
            htmlType="submit"
            loading={loading}
            style={{ float: 'right' }}
          >
            save
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};

export default AddCompanyPool;

import React, { useEffect, useState } from 'react';
import { Button, DatePicker, Form, Input, Select, message } from 'antd';
import FormCom from '@/pages/Licenses/components/FormCom';
import styles from './index.module.scss';
import { useQuery } from 'react-query';
import { getAmpconLicenseDetailOptions } from '@/api/ampcon';
import {
  DeleteOutlined,
  DownOutlined,
  PlusCircleFilled,
} from '@ant-design/icons';
import { useNavigate, useParams } from 'react-router-dom';
import {
  getCompanyAmpconDetail,
  postAddCompanyAmpconForm,
  postAuthDetailList,
} from '@/api/companyAmpcon';
import PcTable from '@/components/Table';
import TableHeader from '../../Companies/AddCompany/components/TableHeader';
import dayjs from 'dayjs';

function index() {
  const [form] = Form.useForm();
  const nodes = Form.useWatch('nodes', form);
  const { id } = useParams();
  const router = useNavigate();
  const [tableParams, setTableParams] = useState({
    current: 1,
    size: 10,
    companyId: id || '',
  });
  const { data: filterOptions } = useQuery(
    'licenses',
    () => getAmpconLicenseDetailOptions(),
    {
      select: (data) => data.data,
    }
  );

  const { data: companyAmpconDetail } = useQuery(
    ['companyAmpconDetail', id],
    () => getCompanyAmpconDetail(id || ''),
    {
      enabled: !!id,
      select: (data) => data.data,
      onSuccess: (data) => {
        form.setFieldsValue({ companyId: data.id });
      },
    }
  );
  const { data: authDetailList } = useQuery(
    ['authDetailList', id, tableParams],
    () => postAuthDetailList(tableParams),
    {
      enabled: !!id,
      select: (data) => data.data,
    }
  );
  console.log(authDetailList);
  const columns = [
    {
      title: 'Software type',
      dataIndex: 'softwareTypeName',
    },
    {
      title: 'Software version',
      dataIndex: 'softwareVersionName',
    },
    {
      title: 'Device type',
      dataIndex: 'deviceTypeName',
    },
    {
      title: 'Feature type',
      dataIndex: 'featureTypeName',
    },
    {
      title: 'License type',
      dataIndex: 'licenseTypeName',
    },
    {
      title: 'Available licenses',
      dataIndex: 'remaining',
    },
    {
      title: 'Expiration date',
      dataIndex: 'expirationDateName',
    },
    {
      title: 'Assigned licenses',
      dataIndex: 'usedQuantity',
    },
  ];

  useEffect(() => {
    form.setFieldsValue({
      nodes: [{ num: '', exp: undefined }],
    });
  }, [id]);

  // 初始化表单数据
  const [softType, setSoftType] = useState(0);
  const [softVersionOptions, setSoftVersionOptions] = useState([]);
  const [deviceTypeOptions, setDeviceTypeOptions] = useState([]);
  const [featureTypeOptions, setFeatureTypeOptions] = useState([]);

  useEffect(() => {
    if (filterOptions && softType) {
      const list = filterOptions.filterList.find(
        (item: any) => item.id === softType
      ).list;
      list.forEach((item: any) => {
        if (item.fieldName === 'Software version') {
          // setSoftVersionOptions(item.children);
        }
        if (item.fieldName === 'Device type') {
          setDeviceTypeOptions(item.children);
        }
        if (item.fieldName === 'Feature type') {
          setFeatureTypeOptions(item.children);
        }
      });
    }
    form.setFieldsValue({
      pattern: filterOptions?.modeList[0].name,
    });
  }, [softType, filterOptions]);

  const handleSoftTypeChange = (value: number) => {
    setSoftType(value);
  };

  // 展开更多
  const [showMore, setShowMore] = useState(false);
  const showMoreChange = () => {
    setShowMore((prevShowMore) => !prevShowMore);
  };
  useEffect(() => {
    if (nodes?.length < 3 && showMore) {
      setShowMore(false);
    }
  }, [nodes]);

  const [submitLoading, setSubmitLoading] = useState(false);

  const handleSubmit = async (values: any) => {
    console.log(values);
    if (id) {
      values.id = id;
    }
    if (values.nodes.length > 0) {
      values.nodes = values.nodes.map((item: any) => {
        return {
          num: item.num,
          exp: item.exp.format('YYYY-MM-DD'),
        };
      });
    }
    setSubmitLoading(true);
    const res = await postAddCompanyAmpconForm(values).finally(() => {
      setSubmitLoading(false);
    });
    if (res.code === 200) {
      form.resetFields();
      message
        .success(id ? 'Update successfully' : 'Add successfully')
        .then(() => {
          router(-1);
        });
    } else {
      message.error(res.message);
    }
  };
  return (
    <FormCom pageTitle="Company AmpCon authorization">
      <Form
        form={form}
        layout="vertical"
        style={{ width: '950px' }}
        onFinish={handleSubmit}
      >
        <Form.Item
          name="companyId"
          label="Company"
          rules={[{ required: true, message: 'Please input company!' }]}
        >
          <Select
            disabled={!!id}
            showSearch
            filterOption={(input, option?: { children: string }) => {
              return (option?.children ?? '')
                .toLowerCase()
                .includes(input.toLowerCase());
            }}
          >
            {filterOptions?.companyList.map((item: any) => (
              <Select.Option key={item.id} value={item.id}>
                {item.name}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item
          name="softwareType"
          label="Software Type"
          rules={[{ required: true, message: 'Please input software type!' }]}
        >
          <Select onChange={handleSoftTypeChange}>
            {filterOptions?.filterList.map((item: any) => (
              <Select.Option key={item.id} value={item.id}>
                {item.name}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item noStyle shouldUpdate>
          {() => {
            const licenseType = form.getFieldValue('licenseType');
            return licenseType === 1 ? null : (
              <>
                {softType !== 2 && (
                  <>
                    {/* <Form.Item
                      name="softwareVersion"
                      label="Software Version"
                      rules={[
                        {
                          required: true,
                          message: 'Please select software version!',
                        },
                      ]}
                    >
                      <Select disabled>
                        {softVersionOptions.map((item: any) => (
                          <Select.Option key={item.id} value={item.id}>
                            {item.name}
                          </Select.Option>
                        ))}
                      </Select>
                    </Form.Item> */}
                    <Form.Item
                      name="deviceType"
                      label="Device Type"
                      rules={[
                        {
                          required: true,
                          message: 'Please select device type!',
                        },
                      ]}
                    >
                      <Select>
                        {deviceTypeOptions.map((item: any) => (
                          <Select.Option key={item.id} value={item.id}>
                            {item.name}
                          </Select.Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </>
                )}
              </>
            );
          }}
        </Form.Item>
        <Form.Item noStyle shouldUpdate>
          {() => {
            const licenseType = form.getFieldValue('licenseType');
            return (
              (licenseType !== 1 || ![1, 10].includes(softType)) && (
                <Form.Item
                  name="featureType"
                  label="Feature Type"
                  rules={[
                    {
                      required: true,
                      message: 'Please select feature type!',
                    },
                  ]}
                >
                  <Select>
                    {featureTypeOptions.map((item: any) => (
                      <Select.Option key={item.id} value={item.id}>
                        {item.name}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              )
            );
          }}
        </Form.Item>
        <Form.Item
          label="License type"
          name="licenseType"
          rules={[
            {
              required: true,
              message: 'Please select license type!',
            },
          ]}
        >
          <Select>
            {filterOptions?.licenseTypeList.map((item: any) => (
              <Select.Option key={item.id} value={item.id}>
                {item.name}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item
          label="Comment"
          name="comment"
          rules={[{ required: true, message: 'Please input comment' }]}
        >
          <Input.TextArea rows={4} placeholder="Comment your changes, please" />
        </Form.Item>
        <Form.List name="nodes">
          {(fields, { add, remove }) => (
            <>
              <div className={styles.form_list_label}>
                <span className={styles.mark}>*</span> License entitlement &
                Expiration date{' '}
                <PlusCircleFilled
                  onClick={() => {
                    add({ num: '', exp: undefined }, 0);
                  }}
                  style={{
                    cursor: 'pointer',
                    color: '#ef5a28',
                    fontSize: '20px',
                    marginLeft: '16px',
                  }}
                />
              </div>
              {fields
                .slice(0, showMore ? fields.length : 3)
                .map((field, index) => (
                  <div className={styles.form_inline_wrap} key={field.key}>
                    <Form.Item
                      name={[field.name, 'num']}
                      rules={[
                        {
                          required: true,
                          message: 'Please input license entitlement!',
                        },
                      ]}
                    >
                      <Input placeholder="License entitlement" />
                    </Form.Item>
                    <div className={styles.after_wrap}>
                      <Form.Item
                        name={[field.name, 'exp']}
                        rules={[
                          { required: true, message: 'Please select date!' },
                        ]}
                        style={{ flex: 1 }}
                      >
                        <DatePicker
                          placeholder="Select date"
                          style={{ width: '289px' }}
                          presets={[
                            {
                              label: '14 days later',
                              value: () => {
                                const selectDate = form.getFieldValue([
                                  'nodes',
                                  field.name,
                                  'exp',
                                ]);
                                return dayjs(selectDate ?? new Date()).add(
                                  13,
                                  'd'
                                ); //当天就算在内
                              },
                            },
                            {
                              label: '104 days later',
                              value: () => {
                                const selectDate = form.getFieldValue([
                                  'nodes',
                                  field.name,
                                  'exp',
                                ]);
                                return dayjs(selectDate ?? new Date()).add(
                                  103,
                                  'd'
                                );
                              },
                            },
                          ]}
                        />
                      </Form.Item>
                      {fields.length > 1 && (
                        <div className={styles.form_list_btn}>
                          <DeleteOutlined
                            style={{
                              fontSize: '20px',
                              cursor: 'pointer',
                            }}
                            onClick={() => remove(index)}
                          />
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              {fields.length > 3 && (
                <div
                  className={styles.form_list_btn_wrap}
                  onClick={showMoreChange}
                >
                  {showMore ? 'See Less' : 'See More'}
                  <span>
                    <DownOutlined
                      style={{
                        transform: showMore ? 'rotate(180deg)' : 'rotate(0deg)',
                        color: '#ef5a28',
                        marginLeft: '10px',
                      }}
                    />
                  </span>
                </div>
              )}
            </>
          )}
        </Form.List>
        {id && (
          <div className={styles.table_wrap}>
            <PcTable
              columns={columns}
              dataSource={authDetailList?.records || []}
              title={() => (
                <TableHeader
                  style={{ margin: -8 }}
                  title="License limitations"
                />
              )}
              pagination={{
                size: 'small',
                total: authDetailList?.total || 0,
                current: tableParams.current,
                showTotal: (total) => `Total ${total} items`,
                onChange: (page, pageSize) => {
                  setTableParams({
                    ...tableParams,
                    current: page,
                    size: pageSize,
                  });
                },
              }}
            ></PcTable>
          </div>
        )}
        <Form.Item>
          <Button type="primary" htmlType="submit" loading={submitLoading}>
            Save
          </Button>
        </Form.Item>
      </Form>
    </FormCom>
  );
}

export default index;

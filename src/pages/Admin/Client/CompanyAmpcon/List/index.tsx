import React, { useState } from 'react';
import { ColumnsType } from 'antd/es/table';
import CommonList from '@/pages/Admin/components/CommonList';
import { Button } from 'antd';
import { Link, useNavigate } from 'react-router-dom';
import { useQuery } from 'react-query';
import { getCompanyAmpconList } from '@/api/companyAmpcon';
import useBreadcrumb from '@/hooks/useBreadcrumb';

type DataType = {
  companyId: number;
  companyName: string;
  available: number;
  assigned: number;
  id: number;
};

type TableParams = {
  current: number;
  size: number;
  keyword: string;
  sortList: { column: string; order: string }[];
  companyId?: number;
};

function index() {
  const router = useNavigate();
  const breadcrumb = useBreadcrumb();
  const [tableParams, setTableParams] = useState<TableParams>({
    current: 1,
    size: 10,
    keyword: '',
    sortList: [],
    companyId: undefined,
  });
  const { data } = useQuery(
    ['companyAmpconList', tableParams],
    () => getCompanyAmpconList(tableParams),
    {
      select: (res) => res.data,
    }
  );
  console.log(data);
  const columns: ColumnsType<DataType> = [
    {
      title: 'Company Name',
      dataIndex: 'companyName',
      render: (text, record) => (
        <Link
          to={`${record.companyId}`}
          onClick={() => breadcrumb(record.companyName)}
        >
          {text}
        </Link>
      ),
    },
    {
      title: 'License created',
      dataIndex: 'usedQuantity',
    },
    {
      title: 'Max license allowed',
      dataIndex: 'available',
    },
  ];
  const handleSearch = (value: string) => {
    setTableParams({
      ...tableParams,
      keyword: value,
    });
  };
  return (
    <CommonList
      pageTitle="Company AmpCon authorization"
      columns={columns}
      dataSource={data?.records || []}
      showAddBtn
      handleSearch={handleSearch}
      pagination={{
        size: 'small',
        total: data?.total || 0,
        current: tableParams.current,
        showTotal: (total) => `Total ${total} items`,
        onChange: (page, pageSize) => {
          setTableParams({
            ...tableParams,
            current: page,
            size: pageSize,
          });
        },
      }}
    >
      <Button type="primary" onClick={() => router('add')}>
        Add Authorization
      </Button>
    </CommonList>
  );
}

export default index;

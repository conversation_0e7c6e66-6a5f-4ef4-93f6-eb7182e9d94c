import React from 'react';
import { useParams } from 'react-router-dom';
import { postCompanyEditLogsDetail } from '@/api/client';
import { useQuery } from 'react-query';
import FormCom from '@/pages/Licenses/components/FormCom';
import styles from './index.module.scss';

const CompanyEditLogsDetail = () => {
  const { id } = useParams();
  const { data } = useQuery(
    ['postUserEditLogsDetail', { logId: id }],
    () => postCompanyEditLogsDetail({ logId: Number(id) }),
    {
      select: (res) => res.data,
    }
  );
  console.log(data);

  return (
    <div>
      <FormCom pageTitle="Company Edit log">
        <p className={styles.title}>Company Name:</p>
        <p className={styles.content}>{data?.oldCompanyName}</p>
        {data?.newCompanyName && (
          <>
            <p className={styles.title}>New Company Name:</p>
            <p className={styles.content}>{data?.newCompanyName}</p>
          </>
        )}
        {data?.oldDescription && (
          <>
            <p className={styles.title}>Old Description:</p>
            <p className={styles.content}>{data?.oldDescription}</p>
          </>
        )}
        {data?.newDescription && (
          <>
            <p className={styles.title}>New Description:</p>
            <p className={styles.content}>{data?.newDescription}</p>
          </>
        )}
        {data?.oldEmail && (
          <>
            <p className={styles.title}>Old Email:</p>
            <p className={styles.content}>{data?.oldEmail}</p>
          </>
        )}
        {data?.newEmail && (
          <>
            <p className={styles.title}>New Email:</p>
            <p className={styles.content}>{data?.newEmail}</p>
          </>
        )}
        {data?.addedCompanyDomains && (
          <>
            <p className={styles.title}>Added Company Domains:</p>
            <p className={styles.content}>{data?.addedCompanyDomains}</p>
          </>
        )}
        {data?.changedCompanyDomains && (
          <>
            <p className={styles.title}>Change Company Domains:</p>
            <p className={styles.content}>{data?.changedCompanyDomains}</p>
          </>
        )}
        {data?.deletedCompanyDomains && (
          <>
            <p className={styles.title}>Deleted Company Domains:</p>
            <p className={styles.content}>{data?.deletedCompanyDomains}</p>
          </>
        )}
        <p className={styles.title}>Comment:</p>
        <p className={styles.content}>{data?.comment}</p>
        <p className={styles.title}>Change date:</p>
        <p className={styles.content}>{data?.changeDate}</p>
      </FormCom>
    </div>
  );
};

export default CompanyEditLogsDetail;

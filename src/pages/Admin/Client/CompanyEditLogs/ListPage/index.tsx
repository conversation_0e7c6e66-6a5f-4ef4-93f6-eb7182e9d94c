import React, { useEffect, useRef, useState } from 'react';
import CommonList from '../../../components/CommonList';
import { useQuery } from 'react-query';
import { getCompanyEditlogPage } from '@/api/client';
import { Link, useNavigate } from 'react-router-dom';
import useBreadcrumb from '@/hooks/useBreadcrumb';
type tableRecordProps = {
  changeDate: string;
  id: number;
  newCompanyName: string;
  oldCompanyName: string;
};
function CompanyEditLogsList() {
  const router = useNavigate();
  const breadcrumb = useBreadcrumb();
  const [page, setPage] = useState({ size: 10, current: 1, keyword: '' });
  const selectedKeys = useRef<React.Key[]>([]);
  const { data, refetch } = useQuery(
    ['getCompanyList', page],
    () => getCompanyEditlogPage(page),
    {
      select: (res) => res.data,
    }
  );
  const columns = [
    {
      title: 'Company edit log',
      dataIndex: 'newCompanyName',
      render: (text: string, record: tableRecordProps) => {
        const name = text
          ? `Company change:${record.oldCompanyName}-${text}-${record.changeDate}`
          : `${record.oldCompanyName}-${record.changeDate}`;
        return (
          <Link
            to={`/admin/client/companyeditlog/${record.id}`}
            onClick={() => breadcrumb(name)}
          >
            {name}
          </Link>
        );
      },
    },
  ];
  const handleSearch = async (value: string) => {
    setPage({ ...page, current: 1, keyword: value });
  };
  return (
    <div>
      <CommonList
        pageTitle="Company edit logs"
        dataSource={data?.records || []}
        columns={columns}
        showAddBtn={false}
        handleSearch={handleSearch}
        footer={undefined}
        pagination={{
          total: data?.total,
          current: page.current,
          showTotal: (total) => `${total} company edit logs`,
          onChange: (current, pageSize) =>
            setPage({ ...page, current, size: pageSize }),
        }}
      ></CommonList>
    </div>
  );
}

export default CompanyEditLogsList;

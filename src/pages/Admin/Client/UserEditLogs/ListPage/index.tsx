import React, { useEffect, useRef, useState } from 'react';
import CommonList from '../../../components/CommonList';
import { useQuery } from 'react-query';
import { getUserEditLogsPage } from '@/api/client';
import { Link, useNavigate } from 'react-router-dom';
import useBreadcrumb from '@/hooks/useBreadcrumb';
type tableRecordProps = {
  changeDate: string;
  id: number;
  newUsername: string;
  oldUsername: string;
};
function CompanyEditLogsList() {
  const router = useNavigate();
  const breadcrumb = useBreadcrumb();
  const [page, setPage] = useState({ size: 10, current: 1, keyword: '' });
  const { data, refetch } = useQuery(
    ['getUserEditLogsPage', page],
    () => getUserEditLogsPage(page),
    {
      select: (res) => res.data,
    }
  );
  const columns = [
    {
      title: 'user edit log',
      dataIndex: 'newUsername',
      render: (text: string, record: tableRecordProps) => {
        const name = text
          ? `${record.oldUsername}>${text}-${record.changeDate}`
          : `${record.oldUsername}-${record.changeDate}`;
        return (
          <Link
            to={`/admin/client/usereditlog/${record.id}`}
            onClick={() => breadcrumb(name)}
          >
            {name}
          </Link>
        );
      },
    },
  ];
  const handleSearch = async (value: string) => {
    setPage({ ...page, current: 1, keyword: value });
  };
  return (
    <div>
      <CommonList
        pageTitle="User edit logs"
        dataSource={data?.records || []}
        columns={columns}
        showAddBtn={false}
        handleSearch={handleSearch}
        footer={undefined}
        pagination={{
          total: data?.total,
          current: page.current,
          showTotal: (total) => `${total} user edit logs`,
          onChange: (current, pageSize) =>
            setPage({ ...page, current, size: pageSize }),
        }}
      ></CommonList>
    </div>
  );
}

export default CompanyEditLogsList;

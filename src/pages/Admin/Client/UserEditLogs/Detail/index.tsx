import React from 'react';
import { useParams } from 'react-router-dom';
import { postUserEditLogsDetail } from '@/api/client';
import { useQuery } from 'react-query';
import FormCom from '@/pages/Licenses/components/FormCom';
import styles from './index.module.scss';

const UserEditLogsDetail = () => {
  const { id } = useParams();
  const { data } = useQuery(
    ['postUserEditLogsDetail', { logId: id }],
    () => postUserEditLogsDetail({ logId: Number(id) }),
    {
      select: (res) => res.data,
    }
  );
  console.log(data);

  return (
    <div>
      <FormCom pageTitle="Users Edit Logs">
        <p className={styles.title}>Username:</p>
        <p className={styles.content}>{data?.oldUsername}</p>
        {data?.newUsername && (
          <>
            <p className={styles.title}>New Username:</p>
            <p className={styles.content}>{data?.newUsername}</p>
          </>
        )}
        <p className={styles.title}>Email:</p>
        <p className={styles.content}>{data?.oldEmail}</p>
        {data?.newEmail && (
          <>
            <p className={styles.title}>New Email:</p>
            <p className={styles.content}>{data?.newEmail}</p>
          </>
        )}
        {data?.oldFirstName && (
          <>
            <p className={styles.title}>Old First Name:</p>
            <p className={styles.content}>{data?.oldFirstName}</p>
          </>
        )}
        {data?.newFirstName && (
          <>
            <p className={styles.title}>New First Name:</p>
            <p className={styles.content}>{data?.newFirstName}</p>
          </>
        )}
        {data?.oldLastName && (
          <>
            <p className={styles.title}>Old Last Name:</p>
            <p className={styles.content}>{data?.oldLastName}</p>
          </>
        )}
        {data?.newLastName && (
          <>
            <p className={styles.title}>New Last Name:</p>
            <p className={styles.content}>{data?.newLastName}</p>
          </>
        )}
        {data?.addedGroup && (
          <>
            <p className={styles.title}>Add Group:</p>
            <p className={styles.content}>{data?.addedGroup}</p>
          </>
        )}
        {data?.addedCompanies && (
          <>
            <p className={styles.title}>Add Companies:</p>
            <p className={styles.content}>{data?.addedCompanies}</p>
          </>
        )}
        {data?.removedGroup && (
          <>
            <p className={styles.title}>Remove Group:</p>
            <p className={styles.content}>{data?.removedGroup}</p>
          </>
        )}
        {data?.removedCompanies && (
          <>
            <p className={styles.title}>Remove Companies:</p>
            <p className={styles.content}>{data?.removedCompanies}</p>
          </>
        )}
        {data?.activeStatus && (
          <>
            <p className={styles.title}>Active Status:</p>
            <p className={styles.content}>{data?.activeStatus}</p>
          </>
        )}
        {data?.superuserStatus && (
          <>
            <p className={styles.title}>Superuser Status:</p>
            <p className={styles.content}>{data?.superuserStatus}</p>
          </>
        )}
        <p className={styles.title}>Change date:</p>
        <p className={styles.content}>{data?.changeDate}</p>
      </FormCom>
    </div>
  );
};

export default UserEditLogsDetail;

import React, { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>confirm, Spin, message, Select } from 'antd';
import CommonList from '../../../components/CommonList';
import { useQuery } from 'react-query';
import { delUser, getUserPageList, userRelationList } from '@/api/client';
import styles from './index.module.scss';
import { useNavigate } from 'react-router-dom';
import { Link } from 'react-router-dom';
import useBreadcrumb from '@/hooks/useBreadcrumb';

enum UserType {
  'All',
  'Partner',
  'Admin',
  'User',
}

function CompaniesList() {
  const router = useNavigate();
  const breadcrumb = useBreadcrumb();
  const userEnum = [
    { label: 'All', value: 0 },
    { label: 'Partner', value: 1 },
    // { label: 'Admin', value: 2 },
  ];
  const [page, setPage] = useState({
    size: 10,
    current: 1,
    username: '',
    userType: 0,
    sortList: [] as any[],
  });
  const selectedKeys = useRef<React.Key[]>([]);
  const [idList, setIdList] = useState<React.Key[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { data, refetch } = useQuery(
    ['getUserPageList', page],
    () => getUserPageList(page),
    {
      select: (res) => res.data,
    }
  );
  const {
    data: relationList,
    isLoading: relationLoading,
    refetch: relationRefetch,
  } = useQuery(['relationList', idList], () => userRelationList({ idList }), {
    select: (res) => res.data,
    enabled: false,
  });
  useEffect(() => {
    if (idList.length > 0) {
      relationRefetch();
    }
  }, [idList, relationRefetch]);
  const columns = [
    {
      title: 'Email address',
      dataIndex: 'emailAddress',
      render: (text: string, record: any) => (
        <Link
          to={`/admin/client/user/${record.id}`}
          onClick={() => breadcrumb(text)}
        >
          {text}
        </Link>
      ),
      sorter: {
        multiple: 1,
      },
    },
    {
      title: 'User type',
      dataIndex: 'userType',
      render: (_: number) => <span>{UserType[_]}</span>,
      // sorter: {
      //   multiple: 2,
      // },
    },
    {
      title: 'License count',
      dataIndex: 'licenseCount',
    },
    {
      title: 'Company name',
      dataIndex: 'companyName',
    },
    {
      title: 'Action',
      render: (text: string, record: any) => (
        <a onClick={() => delItemGroups(record)}>Delete</a>
      ),
    },
  ];
  const rowSelection = {
    onChange: (selectedRowKeys: React.Key[]) => {
      selectedKeys.current = selectedRowKeys;
    },
  };
  const handleSearch = async (value: string) => {
    setPage({ ...page, current: 1, username: value });
  };
  const handleUserType = async (value: number) => {
    setPage({ ...page, current: 1, userType: value });
  };
  const handleDelSelect = async () => {
    const data = await delUser({ idList });
    if (data) {
      message.success('Delete Success!', 2);
      setIsModalOpen(false);
      refetch();
    }
  };
  const closeModal = () => {
    setIsModalOpen(false);
  };
  const delAllGroups = () => {
    if (selectedKeys.current.length > 0) {
      setIdList(selectedKeys.current);
      setIsModalOpen(true);
    } else {
      message.warning('Please select at least one item!', 2);
    }
  };
  const delItemGroups = (record: any) => {
    setIdList([record.id]);
    setIsModalOpen(true);
  };

  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    const sortList = [];
    if (sorter instanceof Array) {
      sorter.forEach((item) => {
        sortList.push({
          column: item.field,
          order: item.order === 'ascend' ? 'ASC' : 'DESC',
        });
      });
    } else {
      if (sorter.order) {
        sortList.push({
          column: sorter.field,
          order: sorter.order === 'ascend' ? 'ASC' : 'DESC',
        });
      }
    }
    setPage({
      ...page,
      current: pagination.current,
      size: pagination.pageSize,
      sortList,
    });
  };
  return (
    <div>
      <CommonList
        pageTitle="Users"
        dataSource={data?.records || []}
        columns={columns}
        showAddBtn={true}
        rowSelection={{
          ...rowSelection,
        }}
        handleDelSelect={delAllGroups}
        handleSearch={handleSearch}
        onChange={handleTableChange}
        pagination={{
          total: data?.total,
          current: page.current,
          showTotal: (total) => `${total} Users`,
          // onChange: (current, pageSize) =>
          //   setPage({ ...page, current, size: pageSize }),
        }}
      >
        <div>
          <Button
            type="primary"
            onClick={() => router('/admin/client/user/add')}
          >
            Add User
          </Button>
          <Select
            defaultValue={0}
            options={userEnum}
            style={{ width: 100, marginLeft: 24 }}
            onChange={handleUserType}
          ></Select>
        </div>
      </CommonList>
      <Modal
        title="Delete User"
        open={isModalOpen}
        onCancel={closeModal}
        onOk={handleDelSelect}
        style={{ paddingBottom: '16px' }}
        destroyOnClose
      >
        <div className={styles.del_modal}>
          {relationLoading ? (
            <Spin size="large" />
          ) : (
            <>
              <p>
                Are you sure you want to delete the selected user? All of the
                following objects and their related items will be deleted:
              </p>
              <div className={styles.content_wrap}>
                {relationList &&
                  relationList.map((item: any, index: number) => (
                    <section key={index}>
                      <div>
                        User: <Link to={item.route}> {item?.username}</Link>
                      </div>
                      <ul>
                        {item.remarkList.map((child: any, index: number) => {
                          return <li key={index}>{child}</li>;
                        })}
                      </ul>
                    </section>
                  ))}
              </div>
            </>
          )}
        </div>
      </Modal>
    </div>
  );
}

export default CompaniesList;

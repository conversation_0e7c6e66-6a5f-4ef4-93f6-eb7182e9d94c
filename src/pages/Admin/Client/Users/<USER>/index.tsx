import React, { useMemo, useState } from 'react';
import FormCom from '@/pages/Licenses/components/FormCom';
import { Button, Form, Input, message, Checkbox, Select } from 'antd';
import TableHeader from '@/pages/Admin/Client/Companies/AddCompany/components/TableHeader';
import styles from './index.module.scss';
import Transfer from '@/components/Transfer';
import { useParams, useNavigate, Link } from 'react-router-dom';
import {
  getUserDetail,
  postAddUser,
  postEditUser,
  queryCompanyPage,
  userDetail,
} from '@/api/client';
import { getAuthGroupsList } from '@/api/auth/';
import { useQuery } from 'react-query';

type userItem = {
  id: string;
  name: string;
};

function formatCompanies(companies: userItem[]) {
  return companies?.map((item) => ({
    key: item.id,
    title: item.name,
  }));
}

const AddUser = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const formItemLayout = { labelCol: { span: 24 }, wrapperCol: { span: 24 } };
  const [page, setPage] = useState({
    size: 10000,
    current: 1,
    companyName: '',
  });
  const [loading, setLoading] = useState<boolean>(false);

  const { data: CompanyList } = useQuery(
    ['queryCompanyPage', page],
    () => queryCompanyPage(page),
    {
      select: (res) => res.data?.records,
    }
  );
  const { data: GroupList } = useQuery(
    ['getGroupsList', page],
    () => getAuthGroupsList(page),
    {
      select: (res) => res.data?.records,
    }
  );

  const { data } = useQuery(
    ['getUserDetail', {}],
    () => getUserDetail({ userId: Number(id) }),
    {
      select: (res) => res.data,
      enabled: !!id,
      onSuccess: (data) => {
        // console.log(data);
        form.setFieldsValue(data);
      },
    }
  );

  const getCompanyName = (): string => {
    if (!id) return '';
    return (
      CompanyList?.filter(
        (c: { id: number }) => c.id === data?.companyIdList[0]
      )[0]?.name || ''
    );
  };
  const onFinish = (values: any) => {
    console.log(values);
    let params = {} as userDetail;
    if (id) {
      params = {
        ...values,
        userId: +id,
        isActive: +values.isActive,
        isSuperuser: +values.isSuperuser,
      };
    } else {
      params = { ...values };
    }
    console.log(params, +!!id);
    setLoading(true);
    const callback = +!!id ? postEditUser : postAddUser;
    try {
      callback(params)
        .then((res) => {
          if (res.code === 200) {
            message.success('Success');
            navigate('/admin/client/user');
          } else {
            message.error(res.message);
          }
          setLoading(false);
        })
        .finally(() => {
          setLoading(false);
        });
    } catch (err) {
      console.error(err);
      setLoading(false);
    }
  };

  const passwordExtra: React.ReactNode = (
    <>
      Raw passwords are not stored, so there is no way to see this user's
      password, but you can change the password using
      <br />
      <Link to={`/admin/client/user/password/${id}`}> this form</Link>.
    </>
  );

  return (
    <div className={styles.AddUser}>
      <Form size="large" {...formItemLayout} form={form} onFinish={onFinish}>
        <FormCom pageTitle="Users">
          <div className={styles.formItemBox}>
            <Form.Item
              label="Username"
              name="username"
              rules={[{ required: true }]}
              extra="First, enter a username and password. Then, you'll be able to edit more user options."
            >
              <Input disabled={!!id} autoComplete="new-password" />
            </Form.Item>
            {!id ? (
              <>
                <Form.Item
                  label="Password"
                  name="password"
                  rules={[{ required: !id }]}
                  extra="Required. 255 characters or fewer. Letters, numbers and @/./+/-/_ characters"
                >
                  <Input.Password autoComplete="new-password" />
                </Form.Item>
                <Form.Item
                  label="Password confirmation:"
                  name="passwordConfirm"
                  rules={[{ required: !id }]}
                >
                  <Input.Password autoComplete="new-password2" />
                </Form.Item>
              </>
            ) : (
              <Form.Item label="Password" extra={passwordExtra}>
                <p className={styles.content}>
                  algorithm: unsalted_sha1 {data?.password}
                </p>
              </Form.Item>
            )}
          </div>
        </FormCom>
        <div className={styles.table_box}>
          <TableHeader title="Personal info" />
          {id && (
            <div className={styles.formItemBox}>
              <Form.Item
                style={{ marginTop: 24 }}
                label="First name"
                name="firstName"
                rules={[{ required: !!id }]}
              >
                <Input />
              </Form.Item>
              <Form.Item
                label="Last name"
                name="lastName"
                rules={[{ required: !!id }]}
              >
                <Input />
              </Form.Item>
            </div>
          )}
          <div className={styles.TransferBox}>
            <Form.Item
              label="Companies"
              name="companyIdList"
              rules={[{ required: true, message: 'Please select Companies!' }]}
            >
              <Transfer
                titles={['Available Companies', 'Chosen Companies']}
                dataSource={formatCompanies(CompanyList)}
              ></Transfer>
            </Form.Item>
          </div>
          <div className={styles.formItemBox}>
            <Form.Item
              label="Email"
              name="email"
              rules={[
                { required: true },
                {
                  pattern: /^[\w.-]+@[\w.-]+\.\w+$/,
                  message: 'Please enter the correct email address',
                },
              ]}
            >
              <Input />
            </Form.Item>
          </div>
          <div className={styles.formItemBox}>
            <Form.Item label="Groups" name="groupIdList">
              <Select
                mode="multiple"
                options={GroupList}
                fieldNames={{ label: 'name', value: 'id' }}
              ></Select>
            </Form.Item>
          </div>
        </div>
        {id && (
          <>
            <div className={styles.table_box}>
              <TableHeader title="Permissions" />
              <Form.Item
                style={{ marginTop: 24 }}
                name="isActive"
                extra="Designates whether this user should be treated as active. Unselect this instead of deleting accounts"
                valuePropName="checked"
              >
                <Checkbox>Active</Checkbox>
              </Form.Item>
              <Form.Item
                name="isSuperuser"
                extra="Designates that this user has all permissions without explicitly assigning them."
                valuePropName="checked"
              >
                <Checkbox>Superuser status</Checkbox>
              </Form.Item>
            </div>
            <div className={styles.table_box}>
              <TableHeader title="Important dates" />
              <p className={styles.title}>Last login:</p>
              <p className={styles.content}>{data?.lastLogin}</p>
              <p className={styles.title}>Date joined:</p>
              <p className={styles.content}>{data?.dateJoined}</p>
              <p className={styles.title}>Company name:</p>
              <p className={styles.content}>{getCompanyName()}</p>
              <p className={styles.title}>Telephone:</p>
              <p className={styles.content}>{data?.telephone}</p>
              <p className={styles.title}>Country:</p>
              <p className={styles.content}>{data?.country}</p>
            </div>
          </>
        )}
        <Form.Item wrapperCol={{ offset: 4, span: 16 }}>
          <Button
            type="primary"
            htmlType="submit"
            loading={loading}
            style={{ float: 'right' }}
          >
            save
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};

export default AddUser;

import { ChangePasswordParams } from '@/api/interface';
import { postChangeUserPassword } from '@/api/user/login';
import FormCom from '@/pages/Licenses/components/FormCom';
import { Button, Form, Input, message } from 'antd';
import { useNavigate, useParams } from 'react-router-dom';

function PasswordChange() {
  const { id } = useParams();
  const [form] = Form.useForm();
  const router = useNavigate();
  const onFinish = async (values: ChangePasswordParams) => {
    const data = await postChangeUserPassword(
      Object.assign({ userId: Number(id) }, values)
    );
    if (data.code === 200) {
      message
        .success('Password changed successfully', 2)
        .then(() => router(-1));
    }
  };
  return (
    <FormCom pageTitle="Users">
      <div style={{ marginBottom: '50px' }}>
        Enter a new password for the user {id} .
      </div>
      <Form
        autoComplete="off"
        style={{ minWidth: 350 }}
        layout="vertical"
        onFinish={onFinish}
        size="large"
        form={form}
      >
        <Form.Item
          label="Password"
          name="password"
          rules={[{ required: true }]}
        >
          <Input.Password />
        </Form.Item>
        <Form.Item
          label="Password (again):"
          name="passwordConfirm"
          dependencies={['password']}
          extra="Enter the same password as above, for verification."
          rules={[
            {
              required: true,
            },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue('password') === value) {
                  return Promise.resolve();
                }
                return Promise.reject(
                  new Error('The two password fields did not match.')
                );
              },
            }),
          ]}
        >
          <Input.Password />
        </Form.Item>
        <Form.Item>
          <Button
            type="primary"
            htmlType="submit"
            style={{ width: '100%', marginTop: '10px' }}
          >
            Change password
          </Button>
        </Form.Item>
      </Form>
    </FormCom>
  );
}

export default PasswordChange;

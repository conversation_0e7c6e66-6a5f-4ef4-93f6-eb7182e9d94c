import React, { useState } from 'react';
import { Input } from 'antd';
const { Search } = Input;
interface NumericInputProps {
  style?: React.CSSProperties;
  onChange: (value: string) => void;
}

const NumericInput = (props: NumericInputProps) => {
  const { onChange } = props;
  const [numberValue, setNumberValue] = useState('');
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value: inputValue } = e.target;
    const reg = /^-?\d*(\.\d*)?$/;
    if (reg.test(inputValue) || inputValue === '' || inputValue === '-') {
      setNumberValue(inputValue);
    }
  };

  // '.' at the end or only '-' in the input box.
  const handleBlur = () => {
    let valueTemp = numberValue;
    if (
      numberValue.charAt(numberValue.length - 1) === '.' ||
      numberValue === '-'
    ) {
      valueTemp = numberValue.slice(0, -1);
    }
    onChange(valueTemp.replace(/0*(\d+)/, '$1'));
  };

  return (
    <Search
      {...props}
      value={numberValue}
      onChange={handleChange}
      onBlur={handleBlur}
      onSearch={(value, event, info) => {
        if (info && info?.source === 'input' && value) {
          onChange(numberValue);
          setNumberValue('');
        }
      }}
      allowClear
      maxLength={16}
      enterButton="Add"
    />
  );
};

export default NumericInput;

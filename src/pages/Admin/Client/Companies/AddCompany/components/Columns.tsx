import { DatePicker, InputNumber, Input } from 'antd';
import dayjs from 'dayjs';
import NumbericInput from './NumericInput';

export default function getColumns(
  callback: <T>(record: T, type: string, i: number, value: T) => void,
  name: string | undefined
) {
  const dateFormat = 'YYYY-MM-DD';

  return [
    {
      title: 'Mode',
      dataIndex: 'mode',
      render: (_: string, records: any) => (
        <>
          <p style={{ fontSize: 9, color: '#666' }}>
            {name}-{records?.speedType}-{records?.featureType}-{records?.limit}(
            {records?.assigned})
          </p>
          <p>{_}</p>
        </>
      ),
    },
    {
      title: 'Speed type',
      dataIndex: 'speedType',
    },
    {
      title: 'Feature type',
      dataIndex: 'featureType',
    },
    {
      title: 'Add',
      key: 'add',
      render: (_: number, records: any, i: number) => {
        return (
          <InputNumber
            style={{ color: 'rgba(0, 0, 0, 0.88)' }}
            min={0}
            max={9999999}
            defaultValue={_}
            onChange={(value) => callback(records, 'add', i, value)}
          />
        );
      },
    },
    {
      title: 'Allocated',
      dataIndex: 'limit',
    },
    {
      title: 'Assigned',
      dataIndex: 'assigned',
    },
    {
      title: 'Expire date',
      dataIndex: 'expireDate',
      render: (_: string, records: any, i: number) => {
        return (
          <DatePicker
            defaultValue={dayjs(_, dateFormat)}
            format={dateFormat}
            onChange={(value) =>
              callback(
                records,
                'expireDate',
                i,
                dayjs(value).format(dateFormat)
              )
            }
          />
        );
      },
    },
    {
      title: 'Production Order Number',
      dataIndex: 'orderNumber',
      render: (_: string, records: any, i: number) => {
        return (
          <Input
            defaultValue={_ || ''}
            placeholder="Enter MO number"
            onChange={(e) =>
              callback(records, 'orderNumber', i, e.target.value)
            }
            style={{ width: '150px' }}
          />
        );
      },
    },
    // {
    //   title: 'Delete?',
    //   dataIndex: 'Delete',
    // },
  ];
}

import React, { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>con<PERSON>rm, Spin, message } from 'antd';
import CommonList from '../../../components/CommonList';
import { useQuery } from 'react-query';
import { Link } from 'react-router-dom';
import {
  delCompany,
  queryCompanyPage,
  companyRelationList,
} from '@/api/client';
import styles from './index.module.scss';
import { useNavigate } from 'react-router-dom';
import useBreadcrumb from '@/hooks/useBreadcrumb';

function CompaniesList() {
  const router = useNavigate();
  const breadcrumb = useBreadcrumb();
  const [page, setPage] = useState({ size: 10, current: 1, companyName: '' });
  const selectedKeys = useRef<React.Key[]>([]);
  const [idList, setIdList] = useState<React.Key[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { data, refetch } = useQuery(
    ['getCompanyList', page],
    () => queryCompanyPage(page),
    {
      select: (res) => res.data,
    }
  );
  const {
    data: relationList,
    isLoading: relationLoading,
    refetch: relationRefetch,
  } = useQuery(
    ['relationList', idList],
    () => companyRelationList({ idList }),
    {
      select: (res) => res.data,
      enabled: false,
    }
  );
  useEffect(() => {
    if (idList.length > 0) {
      relationRefetch();
    }
  }, [idList, relationRefetch]);
  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      render: (text: string, record: any) => (
        <Link
          to={`/admin/client/company/${record.id}`}
          onClick={() => breadcrumb(text)}
        >
          {text}
        </Link>
      ),
    },
    {
      title: 'License Keys created',
      dataIndex: 'licenseKeyCreated',
    },
    {
      title: 'Max license allowed',
      dataIndex: 'maxLicenseAllowed',
    },
    {
      title: 'Action',
      render: (text: string, record: any) => (
        <a onClick={() => delItemGroups(record)}>Delete</a>
      ),
    },
  ];
  const rowSelection = {
    onChange: (selectedRowKeys: React.Key[]) => {
      selectedKeys.current = selectedRowKeys;
    },
  };
  const handleSearch = async (value: string) => {
    setPage({ ...page, current: 1, companyName: value });
  };
  const handleDelSelect = async () => {
    if (relationLoading) return;
    const data = await delCompany({ idList });
    if (data) {
      message.success('Delete Success!', 2);
      setIsModalOpen(false);
      refetch();
    }
  };
  const closeModal = () => {
    setIsModalOpen(false);
  };
  const delAllGroups = () => {
    if (selectedKeys.current.length > 0) {
      setIdList(selectedKeys.current);
      setIsModalOpen(true);
    } else {
      message.warning('Please select at least one item!', 2);
    }
  };
  const delItemGroups = (record: any) => {
    setIdList([record.id]);
    setIsModalOpen(true);
  };
  return (
    <div>
      <CommonList
        pageTitle="Companies"
        dataSource={data?.records || []}
        columns={columns}
        showAddBtn={true}
        rowSelection={{
          ...rowSelection,
        }}
        handleDelSelect={delAllGroups}
        handleSearch={handleSearch}
        pagination={{
          total: data?.total,
          current: page.current,
          showTotal: (total) => `${total} Companies`,
          onChange: (current, pageSize) =>
            setPage({ ...page, current, size: pageSize }),
        }}
      >
        <Button
          type="primary"
          onClick={() => router('/admin/client/company/add')}
        >
          Add Company
        </Button>
      </CommonList>
      <Modal
        title="Delete Companies"
        open={isModalOpen}
        onCancel={closeModal}
        onOk={handleDelSelect}
        style={{ paddingBottom: '16px' }}
        destroyOnClose
      >
        <div className={styles.del_modal}>
          {relationLoading ? (
            <Spin size="large" />
          ) : (
            <>
              <p>
                Are you sure you want to delete the selected company? All of the
                following objects and their related items will be deleted:
              </p>
              <div className={styles.content_wrap}>
                {relationList &&
                  relationList.map((item: any, index: number) => (
                    <section key={index}>
                      <div>
                        Company:
                        <Link to={item.route}> {item?.companyName}</Link>
                      </div>
                      <ul>
                        {item.remarkList.map((child: any, index: number) => {
                          return (
                            <li key={index}>
                              {child.route ? (
                                <Link to={child.route}>{child.desc}</Link>
                              ) : (
                                child.desc
                              )}
                            </li>
                          );
                        })}
                      </ul>
                    </section>
                  ))}
              </div>
            </>
          )}
        </div>
      </Modal>
    </div>
  );
}

export default CompaniesList;

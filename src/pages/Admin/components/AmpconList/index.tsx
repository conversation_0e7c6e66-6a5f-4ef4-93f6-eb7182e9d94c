import Table, { CustomTableProps, DefaultData } from '@/components/Table';
import styles from './index.module.scss';
import { <PERSON><PERSON>, Divider, Popconfirm } from 'antd';
import classNames from 'classnames/bind';

interface CommonListProps<T> extends CustomTableProps<T> {
  pageTitle: string;
  handleDelSelect?: () => void;
  extra?: React.ReactNode;
  title?: () => React.ReactNode;
  headerTop?: React.ReactNode;
  headerBottom?: React.ReactNode;
  popconfirmProps?: {
    title: string;
    description: React.ReactNode;
  };
}
const cx = classNames.bind(styles);

function CommonList<T extends DefaultData>(props: CommonListProps<T>) {
  const { dataSource, columns, pageTitle, handleDelSelect, title, ...rest } =
    props;
  const delSelect = () => {
    handleDelSelect && handleDelSelect();
  };
  return (
    <div className={styles.common_list_wrap}>
      <h1 className={styles.title}>{pageTitle}</h1>
      <main className={styles.content_container}>
        <Table
          title={
            title
              ? title
              : () => (
                  <div className={cx('table_header')}>
                    <div className={cx('header_top')}>{props.headerTop}</div>
                    <Divider style={{ margin: '15px 0' }} />
                    <div className={cx('header_bottom')}>
                      {props.headerBottom}
                    </div>
                  </div>
                )
          }
          dataSource={dataSource}
          columns={columns}
          style={{ width: '100%' }}
          footer={
            rest['rowSelection']
              ? () =>
                  props?.popconfirmProps ? (
                    <div>
                      <Popconfirm
                        onConfirm={delSelect}
                        {...(props.popconfirmProps as any)}
                      >
                        <Button type="primary">Delete All</Button>
                      </Popconfirm>
                    </div>
                  ) : (
                    <Button type="primary" onClick={delSelect}>
                      Delete All
                    </Button>
                  )
              : undefined
          }
          {...rest}
        ></Table>
      </main>
    </div>
  );
}

export default CommonList;

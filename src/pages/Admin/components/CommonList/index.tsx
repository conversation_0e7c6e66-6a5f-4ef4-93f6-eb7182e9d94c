import Table, { CustomTableProps, DefaultData } from '@/components/Table';
import styles from './index.module.scss';
import { Button, Input, Space } from 'antd';
import classNames from 'classnames/bind';

interface CommonListProps<T> extends CustomTableProps<T> {
  pageTitle: string;
  showAddBtn?: boolean;
  showSearch?: boolean;
  handleDelSelect?: () => void;
  handleSearch?: (value: string) => void;
  extra?: React.ReactNode;
}
const cx = classNames.bind(styles);

function CommonList<T extends DefaultData>(props: CommonListProps<T>) {
  const {
    dataSource,
    columns,
    showAddBtn = true,
    showSearch = true,
    pageTitle,
    handleDelSelect,
    handleSearch,
    ...rest
  } = props;
  const delSelect = () => {
    handleDelSelect && handleDelSelect();
  };
  const onSearch = (value: string) => handleSearch && handleSearch(value);
  return (
    <div className={styles.common_list_wrap}>
      <h1 className={styles.title}>{pageTitle}</h1>
      <main className={styles.content_container}>
        <Table
          title={() => (
            <div
              className={cx([
                'table_header',
                showAddBtn ? 'between' : 'flex_end',
              ])}
            >
              {showAddBtn && props.children}
              {showSearch && (
                <Space size={16}>
                  {props.extra}
                  <Input.Search
                    placeholder="input search text"
                    onSearch={onSearch}
                    style={{ width: 200 }}
                    allowClear
                  />
                </Space>
              )}
              {/* <Input.Search
                placeholder="input search text"
                onSearch={onSearch}
                style={{ width: 200 }}
              /> */}
            </div>
          )}
          dataSource={dataSource}
          columns={columns}
          pagination={false}
          style={{ width: '100%' }}
          footer={
            rest['rowSelection']
              ? () => (
                  <div>
                    <Button type="primary" onClick={delSelect}>
                      Delete All
                    </Button>
                  </div>
                )
              : undefined
          }
          {...rest}
        ></Table>
      </main>
    </div>
  );
}

export default CommonList;

import React, { useEffect, useState } from 'react';
import { DatePicker, Form, Select, Input } from 'antd';
import styles from '@/pages/Admin/Ampcon/Licenses/List/index.module.scss';

type FilterProps = {
  onValuesChange: (changedValues: any, allValues: any) => void;
  data: OptionsType | undefined;
  noExpireTime?: boolean;
};

export type OptionsType = {
  companyList: CompanyList[];
  filterItems: FilterItems;
  filterList: FilterList[];
  licenseTypeList: {
    id: number;
    name: string;
  }[];
  opraterTypeList: {
    id: number;
    name: string;
  }[];
  opraterUserList: {
    id: number;
    name: string;
  }[];
};

export interface FilterList {
  fieldName: string;
  id: number;
  list: List[];
  name: string;
  parentId: number;
}
export interface List {
  children: Children[];
  fieldName: string;
}

export interface Children {
  fieldName: string;
  id: number;
  list: any[];
  name: string;
  parentId: number;
}
interface CompanyList {
  id: number;
  name: string;
}

interface FilterItems {
  deviceType: DefaultType[];
  featureType: DefaultType[];
  softwareType: DefaultType[];
  softwareVersion: DefaultType[];
}
interface DefaultType {
  code: string;
  dictId: number;
  field: string;
  id: number;
  name: string;
  parentId: number;
}

function FilterHead(props: FilterProps) {
  const [form] = Form.useForm();
  const softType = Form.useWatch('softwareType', form);
  const { data, noExpireTime } = props;
  const [softVersionOptions, setSoftVersionOptions] = useState([]);
  const [deviceTypeOptions, setDeviceTypeOptions] = useState([]);
  const [featureTypeOptions, setFeatureTypeOptions] = useState([]);
  useEffect(() => {
    console.log('softType change', softType);
    setSoftVersionOptions([]);
    setDeviceTypeOptions([]);
    setFeatureTypeOptions([]);
    form.setFieldsValue({
      softwareVersion: undefined,
      deviceType: undefined,
      featureType: undefined,
    });
    if (data && softType) {
      const list =
        data?.filterList.find((item: any) => item.id === softType)?.list || [];
      list.forEach((item: any) => {
        if (item.fieldName === 'Software version') {
          setSoftVersionOptions(item.children);
        }
        if (item.fieldName === 'Device type') {
          setDeviceTypeOptions(item.children);
        }
        if (item.fieldName === 'Feature type') {
          setFeatureTypeOptions(item.children);
        }
      });
    }
  }, [softType, data]);
  console.log(data);
  return (
    <Form
      form={form}
      layout="inline"
      className={styles.filter_head}
      onValuesChange={(changedValues, allValues) => {
        if ('softwareType' in changedValues) {
          allValues.softwareVersion = undefined;
          allValues.deviceType = undefined;
          allValues.featureType = undefined;
        }
        props.onValuesChange(changedValues, allValues);
      }}
    >
      <Form.Item name="optType">
        <Select
          placeholder="Operator type"
          style={{ width: '150px' }}
          allowClear
        >
          {data?.opraterTypeList.map((item) => (
            <Select.Option key={item.id} value={item.id}>
              {item.name}
            </Select.Option>
          ))}
        </Select>
      </Form.Item>
      <Form.Item name="optUserName">
        <Select
          placeholder="Operator"
          style={{ width: '150px' }}
          allowClear
          showSearch
          filterOption={(input, option?: { children: string }) => {
            return (option?.children ?? '')
              .toLowerCase()
              .includes(input.toLowerCase());
          }}
        >
          {data?.opraterUserList.map((item) => (
            <Select.Option key={item.id} value={item.name}>
              {item.name}
            </Select.Option>
          ))}
        </Select>
      </Form.Item>
      <Form.Item name="optDate">
        <DatePicker.RangePicker
          placeholder={['Operation date：Start date', 'End date']}
        />
      </Form.Item>
      <Form.Item name="companyId">
        <Select
          placeholder="Company"
          style={{ width: '150px' }}
          allowClear
          showSearch
          filterOption={(input, option?: { children: string }) => {
            return (option?.children ?? '')
              .toLowerCase()
              .includes(input.toLowerCase());
          }}
        >
          {data?.companyList.map((item) => (
            <Select.Option key={item.id} value={item.id}>
              {item.name}
            </Select.Option>
          ))}
        </Select>
      </Form.Item>
      <Form.Item name="softwareType">
        <Select
          placeholder="Software type"
          style={{ width: '150px' }}
          allowClear
        >
          {data?.filterList.map((item) => (
            <Select.Option key={item.id} value={item.id}>
              {item.name}
            </Select.Option>
          ))}
        </Select>
      </Form.Item>
      <Form.Item name="softwareVersion">
        <Select
          placeholder="Software version"
          style={{ width: '150px' }}
          allowClear
        >
          {softVersionOptions.map((item: any) => (
            <Select.Option key={item.id} value={item.id}>
              {item.name}
            </Select.Option>
          ))}
        </Select>
      </Form.Item>
      <Form.Item name="deviceType">
        <Select placeholder="Device type" style={{ width: '150px' }} allowClear>
          {deviceTypeOptions.map((item: any) => (
            <Select.Option key={item.id} value={item.id}>
              {item.name}
            </Select.Option>
          ))}
        </Select>
      </Form.Item>
      <Form.Item name="featureType">
        <Select
          placeholder="Feature type"
          style={{ width: '150px' }}
          allowClear
        >
          {featureTypeOptions.map((item: any) => (
            <Select.Option key={item.id} value={item.id}>
              {item.name}
            </Select.Option>
          ))}
        </Select>
      </Form.Item>
      <Form.Item name="licenseType">
        <Select
          placeholder="License type"
          allowClear
          style={{ width: '150px' }}
        >
          {data?.licenseTypeList.map((item) => (
            <Select.Option key={item.id} value={item.id}>
              {item.name}
            </Select.Option>
          ))}
        </Select>
      </Form.Item>
      {!noExpireTime && (
        <Form.Item name="expireTime">
          <DatePicker.RangePicker
            placeholder={['Expire date: Start date', 'End date']}
          />
        </Form.Item>
      )}
    </Form>
  );
}

export default FilterHead;

import React, { useState } from 'react';
import AmpconList from '../../components/AmpconList';
import { useQuery } from 'react-query';
import { getLogsFilter, postAuthLogsList } from '@/api/companyAmpcon';
import { ColumnProps } from 'antd/es/table';
import TableHead from './TableHead';

type DataType = {
  id: number;
  optType: number;
  optUserName: string;
  optDate: string;
  num: number;
  companyId: number;
  softwareType: string;
  softwareVersion: string;
  deviceType: string;
  featureType: string;
  exp: string;
  nameData: string;
  licenseType: number;
};

function index() {
  const [tableParams, setTableParams] = useState({
    size: 10,
    current: 1,
  });
  const { data } = useQuery(
    ['authLogs', tableParams],
    () => postAuthLogsList(tableParams),
    {
      select: (res) => res.data,
    }
  );
  const { data: filterData } = useQuery(
    'authLogsFilterData',
    () => getLogsFilter(),
    {
      select: (res) => res.data,
    }
  );
  console.log(data);
  const columns: ColumnProps<DataType>[] = [
    { title: 'Log id', dataIndex: 'id' },
    {
      title: 'Operation type',
      dataIndex: 'optType',
      render: (text, record) => {
        return record.optType === 1 ? 'Add' : 'Change';
      },
    },
    { title: 'Operator', dataIndex: 'optUserName' },
    { title: 'Operation time', dataIndex: 'optDateName' },
    { title: 'Allowed', dataIndex: 'num' },
    {
      title: 'Company',
      dataIndex: 'companyId',
      render: (text, record) => {
        return record?.nameData ? JSON.parse(record.nameData).companyName : '';
      },
    },
    {
      title: 'Software type',
      dataIndex: 'softwareType',
      render: (text, record) => {
        return record?.nameData
          ? JSON.parse(record.nameData).softwareTypeName
          : '';
      },
    },
    {
      title: 'Software version',
      dataIndex: 'softwareVersion',
      render: (text, record) => {
        return record?.nameData
          ? JSON.parse(record.nameData).softwareVersionName
          : '';
      },
    },
    {
      title: 'Device type',
      dataIndex: 'deviceType',
      render: (text, record) => {
        return record?.nameData
          ? JSON.parse(record.nameData).deviceTypeName
          : '';
      },
    },
    {
      title: 'Feature type',
      dataIndex: 'featureType',
      render: (text, record) => {
        return record?.nameData
          ? JSON.parse(record.nameData).featureTypeName
          : '';
      },
    },
    {
      title: 'License type',
      dataIndex: 'licenseType',
      render: (text, record) => {
        return record?.licenseType === 1 ? 'Trial License' : 'Standard License';
      },
    },
    { title: 'Expiration date', dataIndex: 'expName' },
    {
      title: 'Comment',
      dataIndex: 'comments',
      width: 150,
    },
  ];
  const onValuesChange = (changed: any, allValues: any) => {
    let expireTime = undefined;
    let optDate = undefined;
    if (Array.isArray(allValues.optDate)) {
      optDate = [
        allValues.optDate[0].format('YYYY-MM-DD HH:mm:ss'),
        allValues.optDate[1].format('YYYY-MM-DD HH:mm:ss'),
      ];
    }
    if (Array.isArray(allValues.expireTime)) {
      expireTime = [
        allValues.expireTime[0].format('YYYY-MM-DD'),
        allValues.expireTime[1].format('YYYY-MM-DD'),
      ];
    }
    setTableParams({ ...tableParams, ...allValues, expireTime, optDate });
  };
  return (
    <AmpconList
      pageTitle="AmpCon authorization operation logs"
      columns={columns}
      dataSource={data?.records || []}
      headerTop={
        <TableHead
          onValuesChange={onValuesChange}
          data={filterData}
        ></TableHead>
      }
      pagination={{
        size: 'small',
        total: data?.total || 0,
        current: tableParams.current,
        showTotal: (total) => `Total ${total} items`,
        onChange: (page, pageSize) => {
          setTableParams({
            ...tableParams,
            current: page,
            size: pageSize,
          });
        },
      }}
    ></AmpconList>
  );
}

export default index;

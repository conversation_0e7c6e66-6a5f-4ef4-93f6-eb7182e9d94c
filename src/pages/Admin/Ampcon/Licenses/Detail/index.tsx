import React, { useEffect, useRef, useState } from 'react';
import {
  Button,
  DatePicker,
  Form,
  Input,
  Radio,
  Select,
  Upload,
  UploadProps,
  message,
} from 'antd';
import FormCom from '@/pages/Licenses/components/FormCom';
import styles from './index.module.scss';
import {
  DeleteOutlined,
  DownOutlined,
  FileDoneOutlined,
  PlusCircleFilled,
} from '@ant-design/icons';
import { useQuery } from 'react-query';
import {
  AmpconExpirationDateParams,
  downloadAmpconTemplate,
  downloadAmpconTemplateById,
  getAmpconLicenseDetail,
  getAmpconLicenseDetailOptions,
  getAmpconLicensesExpirationDate,
  saveAmpconLicenses,
  updateAmpconLicenses,
} from '@/api/ampcon';
import { useNavigate, useParams } from 'react-router-dom';
import { downloadByBlob } from '@/utils/utils';
import dayjs from 'dayjs';
import classNames from 'classnames/bind';
const cx = classNames.bind(styles);

const paramsMap = [
  'companyId',
  'deviceType',
  'featureType',
  'licenseType',
  'softwareType',
  'softwareVersion',
];
function index() {
  const [form] = Form.useForm();
  const details = Form.useWatch('details', form);
  const licenseType = Form.useWatch('licenseType', form);
  const { id } = useParams();
  const router = useNavigate();
  const { data: filterOptions } = useQuery(
    'licenses',
    () => getAmpconLicenseDetailOptions(),
    {
      select: (data) => data.data,
    }
  );
  const { data: formData } = useQuery(
    ['license', id],
    () => getAmpconLicenseDetail(id || ''),
    {
      select: (res) => res.data,
      enabled: !!id,
      onSuccess: (data) => {
        console.log(data);
        data.details.forEach((item: any) => {
          item.expireDate = dayjs(item.expireDate);
        });
        form.setFieldsValue({
          ...data,
          expireDate: dayjs(data.expireDate),
        });
        setSoftType(data.softwareType);
      },
    }
  );
  useEffect(() => {
    if (!id) {
      form.setFieldsValue({
        details: [{ hardwareIds: '', expireDate: '' }],
      });
    } else {
      // form.setFieldsValue({ hasFile: true });
    }
  }, [id]);
  // const details = Form.useWatch('details', form);
  // console.log(details);
  // console.log(details);

  // form.setFieldsValue({
  //   details: [{ hardwareIds: '', expireDate: '' }],
  // });
  const [softType, setSoftType] = useState(0);
  const [softVersionOptions, setSoftVersionOptions] = useState([]);
  const [deviceTypeOptions, setDeviceTypeOptions] = useState([]);
  const [featureTypeOptions, setFeatureTypeOptions] = useState([]);
  const [expirationDate, setExpirationDate] = useState([]);
  useEffect(() => {
    console.log('softType change', softType);
    if (filterOptions && softType) {
      const list = filterOptions.filterList.find(
        (item: any) => item.id === softType
      ).list;
      list.forEach((item: any) => {
        if (item.fieldName === 'Software version') {
          setSoftVersionOptions(item.children);
        }
        if (item.fieldName === 'Device type') {
          setDeviceTypeOptions(item.children);
        }
        if (item.fieldName === 'Feature type') {
          setFeatureTypeOptions(item.children);
        }
      });
    }
    form.setFieldsValue({
      pattern: filterOptions?.modeList[0].name,
    });
  }, [softType, filterOptions]);

  const handleSoftTypeChange = (value: number) => {
    setSoftType(value);
  };

  const handleValuesChange = (changedValues: any, allValues: any) => {
    console.log(changedValues, allValues);
    const valuesKeys = Object.keys(allValues);
    const params: { [key: string]: any } = {};
    valuesKeys.forEach((key) => {
      if (paramsMap.includes(key)) {
        params[key] = allValues[key];
      }
    });
    if (
      [1, 10].includes(allValues.softwareType) &&
      allValues.licenseType === 1
    ) {
      params.deviceType = undefined;
      params.featureType = undefined;
      form.setFieldsValue({
        deviceType: undefined,
        featureType: undefined,
      });
    } else if (allValues.softwareType === 2) {
      params.softwareVersion = undefined;
      params.deviceType = undefined;
      form.setFieldsValue({
        softwareVersion: undefined,
        deviceType: undefined,
      });
    }
    Object.keys(changedValues).forEach((key) => {
      if (paramsMap.includes(key)) {
        fetchExpirationDate(params as AmpconExpirationDateParams);
      }
    });
  };
  const fetchExpirationDate = async (data: AmpconExpirationDateParams) => {
    form.setFieldsValue({
      expireDate: undefined,
    });
    const details = form.getFieldValue('details');
    if (details && details.length > 0) {
      details.forEach((item: any, index: number) => {
        form.setFieldValue(['details', index, 'expireDate'], undefined);
      });
    }
    const res = await getAmpconLicensesExpirationDate(data);
    setExpirationDate(res.data);
  };

  // 展开更多
  const [showMore, setShowMore] = useState(false);
  const showMoreChange = () => {
    setShowMore((prevShowMore) => !prevShowMore);
  };

  useEffect(() => {
    if (details?.length < 3 && showMore) {
      setShowMore(false);
    }
  }, [details]);
  // 上传文件配置
  const uploadProps: UploadProps = {
    accept: '.xlsx, .xls, .lic',
    maxCount: 1,
    multiple: false,
    beforeUpload: (file) => {
      if (file.size > 1024 * 1024 * 2) {
        message.error('File must be smaller than 2MB!');
      }
      return false;
    },
  };
  const [downloading, setDownloading] = useState(false);
  // 通过id下载模板
  const downloadTemplate = async () => {
    setDownloading(true);
    const res = await downloadAmpconTemplateById(id || '').finally(() =>
      setDownloading(false)
    );
    downloadByBlob(res);
  };
  // 下载空白模板
  const downloadBlankTemplate = async () => {
    setDownloading(true);
    const res = await downloadAmpconTemplate().finally(() =>
      setDownloading(false)
    );
    downloadByBlob(res);
  };

  const [loading, setLoading] = useState(false);
  const handleSubmit = async (values: any) => {
    console.log(values);
    setLoading(true);
    const formData = new FormData();
    Object.keys(values).forEach((key) => {
      if (values[key] === undefined) {
        return;
      }
      if (key === 'file' && Array.isArray(values.file)) {
        return formData.append('file', values.file[0].originFileObj);
      }
      if (key === 'details' && Array.isArray(values.details)) {
        if (id) {
          const details = values.details.concat(deleteList.current);
          console.log('concat details', details);
          return details.forEach((item: any, index: number) => {
            console.log('detail item', item);
            Object.keys(item).forEach((k) => {
              if (k === 'expireDate' && typeof item[k] === 'object') {
                formData.append(
                  `details[${index}].${k}`,
                  item[k].format('YYYY-MM-DD')
                );
              } else {
                formData.append(
                  `details[${index}].${k}`,
                  item[k] === undefined ? '' : item[k]
                );
              }
            });
          });
        } else {
          return values.details.forEach((item: any, index: number) => {
            formData.append(`details[${index}].hardwareIds`, item.hardwareIds);
            formData.append(`details[${index}].expireDate`, item.expireDate);
          });
        }
      }
      if (key === 'expireDate' && id) {
        return formData.append(
          'expireDate',
          values.expireDate.format('YYYY-MM-DD')
        );
      }
      formData.append(key, values[key]);
    });
    if (softType === 2) {
      formData.append('hasFile', 'true');
    }
    if (id) {
      formData.append('id', id);
      const res = await updateAmpconLicenses(formData).finally(() =>
        setLoading(false)
      );
      if (res.code === 200) {
        if (res.data.isSuccess) {
          message.success('Update success').then(() => router(-1));
        } else {
          if (res.data.errorList.length > 0) {
            const errors = res.data.errorList.map(
              (item: any) => item.errorMsg[0]
            );
            message.error(errors.join(' '), 5);
          } else {
            message.error(res.data.message);
          }
        }
      }
      return;
    }
    const res = await saveAmpconLicenses(formData).finally(() =>
      setLoading(false)
    );
    if (res.code === 200) {
      if (res.data.isSuccess) {
        message.success('Save success').then(() => router(-1));
      } else {
        if (res.data.errorList.length > 0) {
          const errors = res.data.errorList.map(
            (item: any) => item.errorMsg[0]
          );
          message.error(errors.join(' '), 5);
        } else {
          message.error(res.data.message);
        }
      }
    }
  };
  // Ampcon-T 下载hardware file
  const downloadHardFile = () => {
    downloadAsLic(formData.hardwareIdFileContent, formData.fileName);
  };
  const downloadAsLic = (record: string, fileName: string) => {
    const element = document.createElement('a');
    const file = new Blob([record], {
      type: 'text/plain;charset=utf-8',
    });
    element.href = URL.createObjectURL(file);
    element.download = fileName;
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
  };
  // 处理表单删除
  const deleteList = useRef<any[]>([]);
  const handleDelete = (index: number) => {
    const formItem = form.getFieldValue(['details', index]);
    console.log(formItem);
    if (formItem?.id) {
      deleteList.current.push({ ...formItem, deleteFlag: 1 });
    }
    console.log(deleteList.current);
  };
  return (
    <FormCom pageTitle="AmpCon License">
      <Form
        form={form}
        style={{ width: '860px' }}
        layout="vertical"
        size="large"
        onFinish={handleSubmit}
        onValuesChange={handleValuesChange}
      >
        {id ? (
          <>
            <div className={styles.form_inline_wrap}>
              <Form.Item
                name="companyName"
                label="Company"
                rules={[{ required: true, message: 'Please input company!' }]}
              >
                <Input disabled />
              </Form.Item>
              <Form.Item
                name="pattern"
                label="Mode"
                rules={[{ required: true, message: 'Please input mode!' }]}
              >
                <Input disabled />
              </Form.Item>
            </div>
            <div className={styles.form_inline_wrap}>
              <Form.Item
                name="softwareTypeName"
                label="Software Type"
                rules={[
                  { required: true, message: 'Please input software type!' },
                ]}
              >
                <Input disabled />
              </Form.Item>
              {softType !== 2 && (
                <>
                  <Form.Item
                    name="softwareVersionName"
                    label="Software Version"
                    rules={[
                      {
                        required: true,
                        message: 'Please input software version!',
                      },
                    ]}
                  >
                    <Input disabled />
                  </Form.Item>
                  <Form.Item shouldUpdate noStyle>
                    {() => {
                      const licenseType = form.getFieldValue('licenseType');
                      return (
                        licenseType !== 1 && (
                          <>
                            <Form.Item
                              name="deviceTypeName"
                              label="Device Type"
                              rules={[
                                {
                                  required: true,
                                  message: 'Please input device type!',
                                },
                              ]}
                            >
                              <Input disabled />
                            </Form.Item>
                          </>
                        )
                      );
                    }}
                  </Form.Item>
                </>
              )}
              <Form.Item noStyle shouldUpdate>
                {() => {
                  const licenseType = form.getFieldValue('licenseType');
                  return (
                    (licenseType !== 1 || ![1, 10].includes(softType)) && (
                      <Form.Item
                        name="featureTypeName"
                        label="Feature Type"
                        rules={[
                          {
                            required: true,
                            message: 'Please input feature type!',
                          },
                        ]}
                      >
                        <Input disabled />
                      </Form.Item>
                    )
                  );
                }}
              </Form.Item>
            </div>
            <Form.Item
              label="License type"
              name="licenseTypeName"
              rules={[
                { required: true, message: 'Please input license type!' },
              ]}
            >
              <Input disabled />
            </Form.Item>
          </>
        ) : (
          <>
            <div className={styles.form_inline_wrap}>
              <Form.Item
                name="companyId"
                label="Company"
                rules={[{ required: true, message: 'Please input company!' }]}
              >
                <Select
                  showSearch
                  filterOption={(input, option?: { children: string }) => {
                    return (option?.children ?? '')
                      .toLowerCase()
                      .includes(input.toLowerCase());
                  }}
                >
                  {filterOptions?.companyList.map((item: any) => (
                    <Select.Option key={item.id} value={item.id}>
                      {item.name}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
              <Form.Item
                name="pattern"
                label="Mode"
                rules={[{ required: true, message: 'Please input mode!' }]}
              >
                <Input disabled />
              </Form.Item>
            </div>
            <div className={styles.form_inline_wrap}>
              <Form.Item
                name="softwareType"
                label="Software Type"
                rules={[
                  { required: true, message: 'Please input software type!' },
                ]}
              >
                <Select onChange={handleSoftTypeChange}>
                  {filterOptions?.filterList.map((item: any) => (
                    <Select.Option key={item.id} value={item.id}>
                      {item.name}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
              {softType !== 2 && (
                <>
                  <Form.Item
                    name="softwareVersion"
                    label="Software Version"
                    rules={[
                      {
                        required: true,
                        message: 'Please select software version!',
                      },
                    ]}
                  >
                    <Select>
                      {softVersionOptions.map((item: any) => (
                        <Select.Option key={item.id} value={item.id}>
                          {item.name}
                        </Select.Option>
                      ))}
                    </Select>
                  </Form.Item>
                  <Form.Item noStyle shouldUpdate>
                    {() => {
                      const licenseType = form.getFieldValue('licenseType');
                      return (
                        licenseType !== 1 && (
                          <Form.Item
                            name="deviceType"
                            label="Device Type"
                            rules={[
                              {
                                required: true,
                                message: 'Please select device type!',
                              },
                            ]}
                          >
                            <Select>
                              {deviceTypeOptions.map((item: any) => (
                                <Select.Option key={item.id} value={item.id}>
                                  {item.name}
                                </Select.Option>
                              ))}
                            </Select>
                          </Form.Item>
                        )
                      );
                    }}
                  </Form.Item>
                </>
              )}
              <Form.Item noStyle shouldUpdate>
                {() => {
                  const licenseType = form.getFieldValue('licenseType');
                  return (
                    (licenseType !== 1 || ![1, 10].includes(softType)) && (
                      <Form.Item
                        name="featureType"
                        label="Feature Type"
                        rules={[
                          {
                            required: true,
                            message: 'Please select feature type!',
                          },
                        ]}
                      >
                        <Select>
                          {featureTypeOptions.map((item: any) => (
                            <Select.Option key={item.id} value={item.id}>
                              {item.name}
                            </Select.Option>
                          ))}
                        </Select>
                      </Form.Item>
                    )
                  );
                }}
              </Form.Item>
            </div>
            <Form.Item
              label="License type"
              name="licenseType"
              rules={[
                { required: true, message: 'Please select license type!' },
              ]}
            >
              <Select>
                {filterOptions?.licenseTypeList.map((item: any) => (
                  <Select.Option key={item.id} value={item.id}>
                    {item.name}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </>
        )}
        <Form.Item
          label="License name"
          name="licenseName"
          // rules={[{ required: true, message: 'Please input license name!' }]}
        >
          <Input />
        </Form.Item>
        <Form.Item label="Comment" name="comment">
          <Input.TextArea rows={4} placeholder="Comment your changes, please" />
        </Form.Item>
        {softType !== 2 ? (
          <>
            {/* {id ? null : (
              <Form.Item
                label="Addition Method"
                name="hasFile"
                required
                initialValue={false}
              >
                <Radio.Group>
                  <Radio value={false}>Form input</Radio>
                  <Radio value={true}>File upload</Radio>
                </Radio.Group>
              </Form.Item>
            )} */}
            <Form.Item
              label="Addition Method"
              name="hasFile"
              required
              initialValue={false}
            >
              <Radio.Group>
                <Radio value={false}>Form input</Radio>
                <Radio value={true}>File upload</Radio>
              </Radio.Group>
            </Form.Item>
            <Form.Item noStyle shouldUpdate>
              {() => {
                const hasFile = form.getFieldValue('hasFile');
                return hasFile ? (
                  <>
                    <div className={styles.form_list_label}>
                      {id ? null : <span className={styles.mark}>*</span>}{' '}
                      Hardware id & Expiration date{' '}
                    </div>
                    <div className={styles.form_upload_wrap}>
                      <Form.Item
                        name="file"
                        valuePropName="fileList"
                        getValueFromEvent={(e) => e.fileList}
                        rules={[
                          {
                            required: id ? false : true,
                            message: 'Please upload file!',
                          },
                        ]}
                      >
                        <Upload {...uploadProps}>
                          <Button type="primary">Upload</Button>
                        </Upload>
                      </Form.Item>
                      <div className={styles.download_btn}>
                        {id ? (
                          <Button
                            type="primary"
                            ghost
                            onClick={downloadTemplate}
                            loading={downloading}
                          >
                            Download template
                          </Button>
                        ) : (
                          <Button
                            type="primary"
                            ghost
                            onClick={downloadBlankTemplate}
                            loading={downloading}
                          >
                            Blank template
                          </Button>
                        )}
                      </div>
                    </div>
                  </>
                ) : (
                  <>
                    {id ? (
                      <Form.List name="details">
                        {(fields, { add, remove }) => (
                          <>
                            <div className={styles.form_list_label}>
                              <span className={styles.mark}>*</span> Hardware id
                              & Expiration date{' '}
                              {[1, 10].includes(softType) &&
                              licenseType === 1 ? null : (
                                <PlusCircleFilled
                                  onClick={() => {
                                    add({ hardwareId: '', expireDate: '' }, 0);
                                  }}
                                  style={{
                                    cursor: 'pointer',
                                    color: '#ef5a28',
                                    marginLeft: '16px',
                                  }}
                                />
                              )}
                            </div>
                            {fields
                              .slice(0, showMore ? fields.length : 3)
                              .map((field, index) => (
                                <div
                                  className={cx(
                                    'form_list_wrap',
                                    'form_hw_wrap'
                                  )}
                                  key={field.key}
                                >
                                  <Form.Item
                                    name={[field.name, 'hardwareId']}
                                    rules={[
                                      {
                                        required: true,
                                        message:
                                          'Please, provide correct format',
                                        pattern:
                                          /^[0-9a-fA-F]{4}(-[0-9a-fA-F]{4}){3}$/,
                                      },
                                    ]}
                                  >
                                    <Input placeholder="Hardware ID format ZZZZ-ZZZZ-ZZZZ-ZZZZ" />
                                  </Form.Item>
                                  <div className={styles.after_btn}>
                                    <Form.Item shouldUpdate>
                                      {() => {
                                        const expireDate = form.getFieldValue([
                                          'details',
                                          field.name,
                                          'expireDate',
                                        ]);
                                        return expireDate &&
                                          typeof expireDate === 'object' ? (
                                          <Form.Item
                                            name={[field.name, 'expireDate']}
                                            rules={[
                                              {
                                                required: true,
                                                message:
                                                  'Please select expiration date',
                                              },
                                            ]}
                                          >
                                            <DatePicker />
                                          </Form.Item>
                                        ) : (
                                          <Form.Item
                                            name={[field.name, 'expireDate']}
                                            rules={[
                                              {
                                                required: true,
                                                message:
                                                  'Please select expiration date',
                                              },
                                            ]}
                                          >
                                            <Select
                                              placeholder="Select expiration date"
                                              style={{ width: 150 }}
                                            >
                                              {formData?.expirationDateList.map(
                                                (item: string) => (
                                                  <Select.Option
                                                    key={item}
                                                    value={item}
                                                  >
                                                    {item}
                                                  </Select.Option>
                                                )
                                              )}
                                            </Select>
                                          </Form.Item>
                                        );
                                      }}
                                    </Form.Item>
                                    {fields.length > 1 && (
                                      <div className={styles.form_list_btn}>
                                        <DeleteOutlined
                                          style={{
                                            fontSize: '20px',
                                            cursor: 'pointer',
                                          }}
                                          onClick={() => {
                                            handleDelete(field.name);
                                            // form.setFieldValue(
                                            //   [field.name, 'deleteFlag'],
                                            //   true
                                            // );
                                            remove(index);
                                          }}
                                        />
                                      </div>
                                    )}
                                  </div>
                                </div>
                              ))}
                            {fields.length > 3 && (
                              <div
                                className={styles.form_list_btn_wrap}
                                onClick={showMoreChange}
                              >
                                {showMore ? 'See Less' : 'See More'}
                                <span>
                                  <DownOutlined
                                    style={{
                                      transform: showMore
                                        ? 'rotate(180deg)'
                                        : 'rotate(0deg)',
                                      color: '#ef5a28',
                                      marginLeft: '10px',
                                    }}
                                  />
                                </span>
                              </div>
                            )}
                          </>
                        )}
                      </Form.List>
                    ) : (
                      <Form.List name="details">
                        {(fields, { add, remove }) => (
                          <>
                            <div className={styles.form_list_label}>
                              <span className={styles.mark}>*</span> Hardware id
                              & Expiration date{' '}
                              {[1, 10].includes(softType) &&
                              licenseType === 1 ? null : (
                                <PlusCircleFilled
                                  onClick={() => {
                                    add({ hardwareIds: '', expireDate: '' }, 0);
                                  }}
                                  style={{
                                    cursor: 'pointer',
                                    color: '#ef5a28',
                                    marginLeft: '16px',
                                  }}
                                />
                              )}
                            </div>
                            {fields
                              .slice(0, showMore ? fields.length : 3)
                              .map((field, index) => (
                                <div
                                  className={styles.form_list_wrap}
                                  key={field.key}
                                >
                                  <Form.Item
                                    name={[field.name, 'hardwareIds']}
                                    rules={[
                                      {
                                        required: true,
                                        message: 'Please input hardware id',
                                      },
                                    ]}
                                  >
                                    <Input.TextArea
                                      placeholder="Support multiple hw-ids, please use , or ; to separate"
                                      rows={4}
                                    />
                                  </Form.Item>
                                  <Form.Item
                                    name={[field.name, 'expireDate']}
                                    rules={[
                                      {
                                        required: true,
                                        message:
                                          'Please select expiration date',
                                      },
                                    ]}
                                  >
                                    <Select
                                      placeholder="Select expiration date"
                                      style={{ height: '116px' }}
                                    >
                                      {expirationDate.map((item: string) => (
                                        <Select.Option key={item} value={item}>
                                          {item}
                                        </Select.Option>
                                      ))}
                                    </Select>
                                  </Form.Item>
                                  {fields.length > 1 && (
                                    <div className={styles.form_list_btn}>
                                      <DeleteOutlined
                                        style={{
                                          fontSize: '20px',
                                          cursor: 'pointer',
                                        }}
                                        onClick={() => remove(index)}
                                      />
                                    </div>
                                  )}
                                </div>
                              ))}
                            {fields.length > 3 && (
                              <div
                                className={styles.form_list_btn_wrap}
                                onClick={showMoreChange}
                              >
                                {showMore ? 'See Less' : 'See More'}
                                <span>
                                  <DownOutlined
                                    style={{
                                      transform: showMore
                                        ? 'rotate(180deg)'
                                        : 'rotate(0deg)',
                                      color: '#ef5a28',
                                      marginLeft: '10px',
                                    }}
                                  />
                                </span>
                              </div>
                            )}
                          </>
                        )}
                      </Form.List>
                    )}
                  </>
                );
              }}
            </Form.Item>
          </>
        ) : (
          <>
            <div className={styles.form_list_label}>
              <span className={styles.mark}>*</span> Hardware File{' '}
            </div>
            {id ? (
              <>
                <div className={styles.file_wrap} onClick={downloadHardFile}>
                  <FileDoneOutlined />
                  <span className={styles.file_name}>{formData?.fileName}</span>
                </div>
                <p>{formData?.systemMac}</p>
                <Form.Item
                  name="expireDate"
                  label="Expiration date"
                  rules={[
                    {
                      required: true,
                      message: 'Please select expiration date',
                    },
                  ]}
                >
                  <DatePicker />
                </Form.Item>
              </>
            ) : (
              <>
                <div className={styles.form_upload_wrap}>
                  <Form.Item
                    name="file"
                    valuePropName="fileList"
                    getValueFromEvent={(e) => e.fileList}
                    rules={[{ required: true, message: 'Please upload file!' }]}
                  >
                    <Upload {...uploadProps}>
                      <Button type="primary">Upload</Button>
                    </Upload>
                  </Form.Item>
                </div>
                <Form.Item
                  name="expireDate"
                  label="Expiration date"
                  rules={[
                    {
                      required: true,
                      message: 'Please select expiration date',
                    },
                  ]}
                >
                  <Select placeholder="Select expiration date">
                    {expirationDate.map((item: string) => (
                      <Select.Option key={item} value={item}>
                        {item}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </>
            )}
          </>
        )}

        {id && [1, 10].includes(softType) && (
          <>
            <div className={styles.warning_text}>
              If you need to modify or delete a hardware ID, please contact the
              local ampcon-dc team promptly to disable the old device.
            </div>
            <Form.Item name="licenseKey" label="License Key">
              <Input.TextArea autoSize={{ minRows: 1, maxRows: 6 }} disabled />
            </Form.Item>
          </>
        )}
        <Form.Item>
          <Button type="primary" htmlType="submit" loading={loading}>
            Save
          </Button>
        </Form.Item>
      </Form>
    </FormCom>
  );
}

export default index;

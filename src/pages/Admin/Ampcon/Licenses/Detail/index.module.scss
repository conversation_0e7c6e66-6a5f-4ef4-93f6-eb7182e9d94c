.form_inline_wrap {
  display: flex;
  flex-wrap: wrap;

  & > div {
    // flex: 1;
    width: 380px;
    margin-right: 100px;
    &:nth-child(2n) {
      margin-right: 0;
    }
  }
}
.form_list_label {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
  .mark {
    color: #dc4446;
  }
}
.form_list_wrap {
  position: relative;
  display: flex;
  gap: 100px;
  & > div {
    flex: 1;
  }
  .form_list_btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: -25px;
  }
}
.form_hw_wrap {
  & > div {
    flex: none;
    &:first-child {
      width: 350px;
    }
  }
  .after_btn {
    display: flex;
    gap: 16px;
    align-items: baseline;
    .form_list_btn {
      position: static;
    }
  }
}
.form_list_btn_wrap {
  cursor: pointer;
  color: $colorPrimary;
  margin-bottom: 16px;
}
.form_upload_wrap {
  position: relative;
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 36px;
  .download_btn {
    position: absolute;
    top: 0;
    left: 150px;
  }
}
.warning_text {
  color: $colorPrimary;
  padding-bottom: 32px;
}
.file_wrap {
  padding-bottom: 16px;
  cursor: pointer;
  .file_name {
    margin-left: 8px;
    color: $colorPrimary;
    &:hover {
      text-decoration: underline;
    }
  }
}

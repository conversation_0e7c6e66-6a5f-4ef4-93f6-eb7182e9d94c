import React, { useEffect, useState } from 'react';
import { DatePicker, Form, Select, Input } from 'antd';
import styles from '../List/index.module.scss';

type FilterProps = {
  onValuesChange: (changedValues: any, allValues: any) => void;
  data: OptionsType | undefined;
};

export type OptionsType = {
  companyList: CompanyList[];
  filterItems: FilterItems;
  filterList: FilterList[];
  licenseTypeList: {
    id: number;
    name: string;
  }[];
};
export interface FilterList {
  fieldName: string;
  id: number;
  list: List[];
  name: string;
  parentId: number;
}
export interface List {
  children: Children[];
  fieldName: string;
}

export interface Children {
  fieldName: string;
  id: number;
  list: any[];
  name: string;
  parentId: number;
}

export interface LicenseTypeList {
  id: number;
  name: string;
}
interface CompanyList {
  id: number;
  name: string;
}

interface FilterItems {
  deviceType?: DefaultType[];
  featureType?: DefaultType[];
  softwareType?: DefaultType[];
  softwareVersion?: DefaultType[];
}
interface DefaultType {
  code: string;
  dictId: number;
  field: string;
  id: number;
  name: string;
  parentId: number;
}

function FilterHead(props: FilterProps) {
  const [form] = Form.useForm();
  const softType = Form.useWatch('softwareType', form);
  const { data: filterOptions } = props;
  console.log(filterOptions);
  const [softVersionOptions, setSoftVersionOptions] = useState([]);
  const [deviceTypeOptions, setDeviceTypeOptions] = useState([]);
  const [featureTypeOptions, setFeatureTypeOptions] = useState([]);
  useEffect(() => {
    console.log('softType change', softType);
    setSoftVersionOptions([]);
    setDeviceTypeOptions([]);
    setFeatureTypeOptions([]);
    form.setFieldsValue({
      softwareVersion: undefined,
      deviceType: undefined,
      featureType: undefined,
    });
    if (filterOptions && softType) {
      const list =
        filterOptions?.filterList.find((item: any) => item.id === softType)
          ?.list || [];
      list.forEach((item: any) => {
        if (item.fieldName === 'Software version') {
          setSoftVersionOptions(item.children);
        }
        if (item.fieldName === 'Device type') {
          setDeviceTypeOptions(item.children);
        }
        if (item.fieldName === 'Feature type') {
          setFeatureTypeOptions(item.children);
        }
      });
    }
  }, [softType, filterOptions]);
  return (
    <Form
      form={form}
      layout="inline"
      className={styles.filter_head}
      onValuesChange={(changedValues, allValues) => {
        if ('softwareType' in changedValues) {
          allValues.softwareVersion = undefined;
          allValues.deviceType = undefined;
          allValues.featureType = undefined;
        }
        props.onValuesChange(changedValues, allValues);
      }}
    >
      <Form.Item name="companyId">
        <Select
          placeholder="Company"
          style={{ width: '150px' }}
          allowClear
          showSearch
          filterOption={(input, option?: { children: string }) => {
            return (option?.children ?? '')
              .toLowerCase()
              .includes(input.toLowerCase());
          }}
        >
          {filterOptions?.companyList.map((item) => (
            <Select.Option key={item.id} value={item.id}>
              {item.name}
            </Select.Option>
          ))}
        </Select>
      </Form.Item>
      <Form.Item name="softwareType">
        <Select
          placeholder="Software type"
          style={{ width: '150px' }}
          allowClear
        >
          {filterOptions?.filterList.map((item: any) => (
            <Select.Option key={item.id} value={item.id}>
              {item.name}
            </Select.Option>
          ))}
        </Select>
      </Form.Item>
      <Form.Item name="softwareVersion">
        <Select
          placeholder="Software version"
          style={{ width: '150px' }}
          allowClear
        >
          {softVersionOptions.map((item: any) => (
            <Select.Option key={item.id} value={item.id}>
              {item.name}
            </Select.Option>
          ))}
        </Select>
      </Form.Item>
      <Form.Item name="deviceType">
        <Select placeholder="Device type" style={{ width: '150px' }} allowClear>
          {deviceTypeOptions.map((item: any) => (
            <Select.Option key={item.id} value={item.id}>
              {item.name}
            </Select.Option>
          ))}
        </Select>
      </Form.Item>
      <Form.Item name="featureType">
        <Select
          placeholder="Feature type"
          style={{ width: '150px' }}
          allowClear
        >
          {featureTypeOptions.map((item: any) => (
            <Select.Option key={item.id} value={item.id}>
              {item.name}
            </Select.Option>
          ))}
        </Select>
      </Form.Item>
      <Form.Item name="licenseType">
        <Select
          placeholder="License type"
          allowClear
          style={{ width: '150px' }}
        >
          {filterOptions?.licenseTypeList.map((item) => (
            <Select.Option key={item.id} value={item.id}>
              {item.name}
            </Select.Option>
          ))}
        </Select>
      </Form.Item>
      <Form.Item name="hardwareId">
        <Input.Search placeholder="Hardware ID" allowClear />
      </Form.Item>
      <Form.Item name="keyword">
        <Input.Search placeholder="License name" allowClear />
      </Form.Item>
      <Form.Item name="createTime">
        <DatePicker.RangePicker
          showTime
          placeholder={['Create date: Start date', 'End date']}
        />
      </Form.Item>
      <Form.Item name="expireTime">
        <DatePicker.RangePicker
          placeholder={['Expire date: Start date', 'End date']}
        />
      </Form.Item>
      <Form.Item name="id">
        <Input.Search placeholder="License ID" allowClear />
      </Form.Item>
    </Form>
  );
}

export default FilterHead;

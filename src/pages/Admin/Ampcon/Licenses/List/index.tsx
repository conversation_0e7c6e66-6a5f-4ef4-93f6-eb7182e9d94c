import React, { Key, useState } from 'react';
import AmpconList from '@/pages/Admin/components/AmpconList';
import { useQuery } from 'react-query';
import {
  LicensesPageParams,
  delAmpconLicense,
  getAmpconLicensesList,
  getAmpconLicensesListFilter,
  getHwIdsById,
} from '@/api/ampcon';
import { ColumnsType } from 'antd/es/table';
import { Button, Popconfirm, Popover, Spin, message } from 'antd';
import FilterHead, { OptionsType } from '../components/FilterHead';
import { Link, useNavigate } from 'react-router-dom';
import useBreadcrumb from '@/hooks/useBreadcrumb';
import styles from './index.module.scss';
import { debounce } from '@/utils/utils';

interface DataType {
  companyId: number;
  companyName: string;
  createTime: string;
  deviceType: number;
  deviceTypeName: string;
  featureType: number;
  featureTypeName: string;
  id: number;
  licenseName: string;
  pattern: string;
  softwareType: number;
  softwareTypeName: string;
  softwareVersion: number;
  softwareVersionName: string;
  totalHwIdsNum: number;
}
type HwIdsDataType = {
  deviceId: string;
  expireDate: string;
  hardwareFileContent: string;
  hardwareId: string;
  hasFailure: number;
  stringExpireDate: string;
  systemMac: string;
};

function index() {
  const [tableParams, setTableParams] = useState<LicensesPageParams>({
    current: 1,
    size: 10,
  });
  const [selectedKeys, setSelectedKeys] = useState<{
    [key: number]: React.Key[];
  }>({ [tableParams.current]: [] });
  const breadcrumb = useBreadcrumb();
  const router = useNavigate();
  const [loading, setLoading] = useState(false);
  const [hwIdsData, setHwIdsData] = useState<HwIdsDataType[]>([]);
  const { data, refetch } = useQuery(
    ['parameter list', tableParams],
    () => getAmpconLicensesList(tableParams),
    {
      select: (res) => res.data,
    }
  );
  const { data: optionsData } = useQuery(
    'getFilter',
    () => getAmpconLicensesListFilter(),
    {
      select: (res) => res.data as OptionsType,
    }
  );
  const columns: ColumnsType<DataType> = [
    {
      title: 'License id',
      dataIndex: 'id',
      fixed: 'left',
      render: (text, record) => (
        <Link
          to={`${record.id}`}
          onClick={() => breadcrumb(record.licenseName)}
        >
          {text}
        </Link>
      ),
    },
    {
      title: 'License Name',
      dataIndex: 'licenseName',
    },
    {
      title: 'Total items',
      dataIndex: 'totalHwIdsNum',
      render: (text, record) => (
        <Popover
          placement="right"
          content={renderHwIdsList}
          onOpenChange={(open) => handlePopoverOpen(open, record.id)}
        >
          <span className={styles.num_text}>{text}</span>
        </Popover>
      ),
    },
    {
      title: 'Create time',
      dataIndex: 'createTime',
    },
    {
      title: 'Company',
      dataIndex: 'companyName',
    },
    {
      title: 'Software type',
      dataIndex: 'softwareTypeName',
    },
    {
      title: 'Software version',
      dataIndex: 'softwareVersionName',
    },
    {
      title: 'Device type',
      dataIndex: 'deviceTypeName',
      render: (text, record) => <span>{text ? text : '-'}</span>,
    },
    {
      title: 'Feature type',
      dataIndex: 'featureTypeName',
    },
    {
      title: 'License type',
      dataIndex: 'licenseTypeName',
    },
    {
      title: 'Mode',
      dataIndex: 'pattern',
    },
    {
      title: 'Action',
      render: (text, record) => (
        <Popconfirm
          style={{ width: '200px' }}
          title="Are you sure to delete this item?"
          description={
            <div style={{ width: 250 }}>
              <p>
                For AmpCon-DC, if you need to delete hardware IDs, please
                promptly contact the local AmpCon-DC team to disable the old
                devices.{' '}
              </p>
              <p>
                For AmpCon-T, if you need to delete a license, please also
                confirm with the Ampcon-T team.
              </p>
            </div>
          }
          onConfirm={() => delItem(record.id)}
        >
          <a>Delete</a>
        </Popconfirm>
      ),
    },
  ];
  const handlePopoverOpen = (status: boolean, id: number) => {
    if (status) {
      setLoading(true);
      fetchHwIdsNum(id);
    }
  };
  const fetchHwIdsNum = async (id: number) => {
    const data = await getHwIdsById(id).finally(() => setLoading(false));
    setHwIdsData(data.data);
  };
  const renderHwIdsList = () => {
    return (
      <Spin spinning={loading}>
        <div className={styles.hw_list_wrap}>
          {hwIdsData.length > 0 ? (
            hwIdsData.map((item) => (
              <div key={item.hardwareId} className={styles.hw_list_item}>
                {item.hardwareId.length > 0 ? (
                  <>
                    <span>{item.stringExpireDate}</span>
                    <span>{item.hardwareId}</span>
                    <span>{item.hasFailure === 1 ? 'Failure' : 'Normal'}</span>
                  </>
                ) : (
                  <>
                    <span>{item.systemMac}</span>
                    <span>{item.stringExpireDate}</span>
                  </>
                )}
              </div>
            ))
          ) : (
            <div>No data</div>
          )}
        </div>
      </Spin>
    );
  };
  const onValuesChange = debounce((changed: any, allValues: any) => {
    let expireTime = undefined;
    let createTime = undefined;
    if (Array.isArray(allValues.createTime)) {
      createTime = [
        allValues.createTime[0].format('YYYY-MM-DD HH:mm:ss'),
        allValues.createTime[1].format('YYYY-MM-DD HH:mm:ss'),
      ];
    }
    if (Array.isArray(allValues.expireTime)) {
      expireTime = [
        allValues.expireTime[0].format('YYYY-MM-DD'),
        allValues.expireTime[1].format('YYYY-MM-DD'),
      ];
    }
    setTableParams({ ...tableParams, ...allValues, expireTime, createTime });
  }, 500);
  const headerBottom = (
    <Button type="primary" onClick={() => router('add')}>
      Add AmpCon license
    </Button>
  );
  const rowSelection = {
    onChange: (selectedRowKeys: React.Key[]) => {
      const temp = { ...selectedKeys };
      temp[tableParams.current] = selectedRowKeys;
      setSelectedKeys(temp);
    },
    selectedRowKeys: selectedKeys[tableParams.current],
  };
  const handleDelSelect = () => {
    const idOrIds = Object.values(selectedKeys).reduce(
      (pre, cur) => [...pre, ...cur],
      []
    );
    if (idOrIds.length > 0) {
      fetchDelSelect(idOrIds);
    } else {
      message.warning('Please select at least one item!', 2);
    }
  };
  const delItem = async (id: number) => {
    fetchDelSelect([id]);
  };
  const fetchDelSelect = async (ids: Key[]) => {
    const data = await delAmpconLicense({ ids });
    if (data) {
      message.success('Delete Success!', 2);
      refetch();
      setSelectedKeys({ [tableParams.current]: [] });
    }
  };
  return (
    <AmpconList
      pageTitle="AmpCon licenses"
      columns={columns}
      scroll={{ x: 1200 }}
      dataSource={data?.records || []}
      headerTop={
        <FilterHead onValuesChange={onValuesChange} data={optionsData} />
      }
      headerBottom={headerBottom}
      rowSelection={{
        ...rowSelection,
      }}
      handleDelSelect={handleDelSelect}
      pagination={{
        size: 'small',
        total: data?.total || 0,
        current: tableParams.current,
        showTotal: (total) => `Total ${total} items`,
        onChange: (page, pageSize) => {
          setTableParams({
            ...tableParams,
            current: page,
            size: pageSize,
          });
        },
      }}
      popconfirmProps={{
        title: 'Are you sure to delete these items?',
        description: (
          <div style={{ width: 250 }}>
            <p>
              For Ampcon-DC, if you need to delete hardware IDs, please promptly
              contact the local Ampcon-DC team to disable the old devices.{' '}
            </p>
            <p>
              For Ampcon-T, if you need to delete a license, please also confirm
              with the Ampcon-T team.
            </p>
          </div>
        ),
      }}
    ></AmpconList>
  );
}

export default index;

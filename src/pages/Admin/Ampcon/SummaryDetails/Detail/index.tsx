import { Button, Form, Input, message } from 'antd';
import { useQuery } from 'react-query';
import { useNavigate, useParams } from 'react-router-dom';
import useSetGlobalLoading from '@/hooks/useSetGlobalLoading';
import FormCom from '@/pages/Licenses/components/FormCom';
import {
  getAmpconLicenseSummaryDetail,
  putAmpconSummary,
} from '@/api/companyAmpcon';

function AmpconSummaryDetail() {
  const router = useNavigate();
  const [form] = Form.useForm();
  const { id } = useParams();
  const { isLoading } = useQuery(
    ['summaryDetailForm', id],
    () => getAmpconLicenseSummaryDetail(id || ''),
    {
      select: (res) => res.data,
      onSuccess: (data) => {
        console.log(data);
        form.setFieldsValue(data);
      },
    }
  );
  useSetGlobalLoading(isLoading);
  const onFinish = async (values: any) => {
    console.log(values);
    const params = {
      remaining: values.remaining,
      comments: values.comments,
      id: id || '',
    };
    const data = await putAmpconSummary(params);
    if (data) {
      message.success('Updated successfully', 1).then(() => {
        router(-1);
      });
    }
  };
  return (
    <FormCom pageTitle="AmpCon license summary details">
      <Form
        style={{ width: '600px' }}
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 14 }}
        form={form}
        onFinish={onFinish}
      >
        <Form.Item
          label="Available Licenses"
          name="remaining"
          rules={[
            {
              required: true,
              pattern: /^[0-9]*$/g,
              message: 'Please input number',
            },
          ]}
        >
          <Input />
        </Form.Item>
        <Form.Item label="Company" name="companyName">
          <Input disabled />
        </Form.Item>
        <Form.Item label="Mode" name="pattern">
          <Input disabled />
        </Form.Item>
        <Form.Item label="Software type" name="softwareTypeName">
          <Input disabled />
        </Form.Item>
        <Form.Item label="Device type" name="deviceTypeName">
          <Input disabled />
        </Form.Item>
        <Form.Item label="Feature type" name="featureTypeName">
          <Input disabled />
        </Form.Item>
        <Form.Item label="License type" name="licenseTypeName">
          <Input disabled />
        </Form.Item>
        <Form.Item label="Expiration date" name="expirationDateName">
          <Input disabled />
        </Form.Item>
        <Form.Item label="Assigned Licenses" name="usedQuantity">
          <Input disabled />
        </Form.Item>
        <Form.Item label="Comment" name="comments">
          <Input.TextArea />
        </Form.Item>
        <Form.Item
          wrapperCol={{
            offset: 19,
          }}
        >
          <Button type="primary" htmlType="submit">
            Save
          </Button>
        </Form.Item>
      </Form>
    </FormCom>
  );
}

export default AmpconSummaryDetail;

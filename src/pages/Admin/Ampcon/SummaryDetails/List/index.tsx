import React, { useState } from 'react';
import { ColumnsType } from 'antd/es/table';
import { useQuery } from 'react-query';
import { postAuthDetailList } from '@/api/companyAmpcon';
import AmpconList from '@/pages/Admin/components/AmpconList';
import FilterHead, { OptionsType } from '../components/FilterHead';
import { getAmpconLicensesListFilter } from '@/api/ampcon';
import { Link } from 'react-router-dom';
import useBreadcrumb from '@/hooks/useBreadcrumb';

type DataType = {
  companyId: string;
  companyName: string;
  mode: string;
  softwareType: string;
  softwareVersion: string;
  deviceType: string;
  featureType: string;
  licenseType: number;
  newAvailable: number;
  exp: string;
  id: number;
  nameData: string;
};

function index() {
  const [tableParams, setTableParams] = useState({ current: 1, size: 10 });
  const breadcrumb = useBreadcrumb();
  const { data } = useQuery(
    ['ampcon-summary-details-list', tableParams],
    () => postAuthDetailList(tableParams),
    {
      select: (res) => res.data,
    }
  );
  console.log(data);
  const { data: optionsData } = useQuery(
    'getFilter',
    () => getAmpconLicensesListFilter(),
    {
      select: (res) => res.data as OptionsType,
    }
  );
  const columns: ColumnsType<DataType> = [
    {
      title: 'Company',
      dataIndex: 'companyName',
      render: (text, record) => (
        <Link
          to={`${record.id}`}
          onClick={() => {
            breadcrumb(record.companyName);
          }}
        >
          {text}
        </Link>
      ),
    },
    {
      title: 'Mode',
      dataIndex: 'pattern',
    },
    {
      title: 'Software type',
      dataIndex: 'softwareTypeName',
    },
    {
      title: 'Software version',
      dataIndex: 'softwareVersionName',
    },
    {
      title: 'Device type',
      dataIndex: 'deviceTypeName',
    },
    {
      title: 'Feature type',
      dataIndex: 'featureTypeName',
    },
    {
      title: 'License type',
      dataIndex: 'licenseTypeName',
    },
    {
      title: 'Available licenses',
      dataIndex: 'remaining',
    },
    {
      title: 'Expiration date',
      dataIndex: 'expirationDateName',
    },
    {
      title: 'Assigned licenses',
      dataIndex: 'usedQuantity',
    },
  ];
  const onValuesChange = (changed: any, allValues: any) => {
    let expireTime = undefined;
    let createTime = undefined;
    if (Array.isArray(allValues.createTime)) {
      createTime = [
        allValues.createTime[0].format('YYYY-MM-DD HH:mm:ss'),
        allValues.createTime[1].format('YYYY-MM-DD HH:mm:ss'),
      ];
    }
    if (Array.isArray(allValues.expireTime)) {
      expireTime = [
        allValues.expireTime[0].format('YYYY-MM-DD'),
        allValues.expireTime[1].format('YYYY-MM-DD'),
      ];
    }
    setTableParams({ ...tableParams, ...allValues, expireTime, createTime });
  };
  return (
    <AmpconList
      pageTitle="AmpCon license summary details"
      columns={columns}
      dataSource={data?.records || []}
      headerTop={
        <FilterHead onValuesChange={onValuesChange} data={optionsData} />
      }
      pagination={{
        size: 'small',
        total: data?.total || 0,
        current: tableParams.current,
        showTotal: (total) => `Total ${total} items`,
        onChange: (page, pageSize) => {
          setTableParams({
            ...tableParams,
            current: page,
            size: pageSize,
          });
        },
      }}
    ></AmpconList>
  );
}

export default index;

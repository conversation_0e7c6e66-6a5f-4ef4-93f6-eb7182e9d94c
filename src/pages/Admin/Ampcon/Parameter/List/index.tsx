import React, { Key, useMemo, useRef, useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import AmpconList from '@/pages/Admin/components/AmpconList';
import { Popconfirm, Button, Space, Select, Input, message, Modal } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { useQuery } from 'react-query';
import {
  delParameterConfig,
  getParameterConfigList,
  getParameterConfigListFilter,
  getSelectDeleteDetail,
} from '@/api/ampcon';
import useBreadcrumb from '@/hooks/useBreadcrumb';

interface DataType {
  id: number;
  name: string;
  parameter: string;
  softwareType: string;
}
type TableParams = {
  current: number;
  size: number;
  sortList?: { column: string; order: string }[];
  name?: string;
  parameter?: number;
  softwareType?: number;
};

function index() {
  const navigate = useNavigate();
  const breadcrumb = useBreadcrumb();
  const [tableParams, setTableParams] = useState<TableParams>({
    current: 1,
    size: 10,
    sortList: [],
    parameter: undefined,
    softwareType: undefined,
  });
  const [selectedKeys, setSelectedKeys] = useState<{
    [key: number]: React.Key[];
  }>({ [tableParams.current]: [] });
  const columns: ColumnsType<DataType> = [
    {
      title: 'Name',
      dataIndex: 'name',
      render: (text, record) => (
        <Link to={`${record.id}`} onClick={() => breadcrumb(record.name)}>
          {text}
        </Link>
      ),
    },
    {
      title: 'Parameter',
      dataIndex: 'parameter',
    },
    {
      title: 'Software type',
      dataIndex: 'softwareType',
    },
    {
      title: 'Action',
      render: (text, record) => (
        <Popconfirm
          title="Are you sure to delete this item?"
          onConfirm={() => delItem(record.id)}
        >
          <a>Delete</a>
        </Popconfirm>
      ),
    },
  ];

  const { data, refetch } = useQuery(
    ['parameter list', tableParams],
    () => getParameterConfigList(tableParams),
    {
      select: (res) => res.data,
    }
  );
  const { data: filterData } = useQuery(
    'filterData',
    () => getParameterConfigListFilter(),
    {
      select: (res) => res.data,
    }
  );
  console.log(filterData);
  const softwareTypeOptions = useMemo(() => {
    return (
      filterData?.length > 0 &&
      filterData[0].list.map((item: any) => (
        <Select.Option key={item.id} value={item.id}>
          {item.name}
        </Select.Option>
      ))
    );
  }, [filterData]);
  const parameterOptions = useMemo(() => {
    return (
      filterData?.length > 0 &&
      filterData[0].children.map((item: any) => (
        <Select.Option key={item.id} value={item.id}>
          {item.name}
        </Select.Option>
      ))
    );
  }, [filterData]);

  async function delItem(id: number) {
    fetchDelData([id]);
  }
  const fetchDelSelect = async (ids: React.Key[]) => {
    const data = await delParameterConfig({ ids });
    if (data) {
      message.success('Delete Success!', 2);
      refetch();
      setSelectedKeys({ [tableParams.current]: [] });
    }
  };
  const rowSelection = {
    onChange: (selectedRowKeys: React.Key[]) => {
      const temp = { ...selectedKeys };
      temp[tableParams.current] = selectedRowKeys;
      setSelectedKeys(temp);
    },
    selectedRowKeys: selectedKeys[tableParams.current],
  };
  const handleDelSelect = () => {
    const idOrIds = Object.values(selectedKeys).reduce(
      (pre, cur) => [...pre, ...cur],
      []
    );
    if (idOrIds.length > 0) {
      fetchDelData(idOrIds);
    } else {
      message.warning('Please select at least one item!', 2);
    }
  };
  const headerTop = (
    <Space size="large">
      <Select
        placeholder="Software type"
        allowClear
        onChange={(value) =>
          setTableParams({
            ...tableParams,
            softwareType: value,
          })
        }
      >
        {softwareTypeOptions}
      </Select>
      <Select
        style={{ width: 200 }}
        placeholder="Parameter"
        allowClear
        onChange={(value) =>
          setTableParams({
            ...tableParams,
            parameter: value,
          })
        }
      >
        {parameterOptions}
      </Select>
      <Input.Search
        placeholder="input search name"
        style={{ width: 200 }}
        allowClear
        onSearch={(value) =>
          setTableParams({
            ...tableParams,
            name: value,
          })
        }
      />
    </Space>
  );

  const headerBottom = (
    <Button type="primary" onClick={() => navigate('add')}>
      Add Parameter
    </Button>
  );

  const [visible, setVisible] = useState(false);
  const [delData, setDelData] = useState<any[]>([]);
  const fetchDelData = async (ids: React.Key[]) => {
    const res = await getSelectDeleteDetail({ ids });
    if (res.data.length > 0) {
      setVisible(true);
      setDelData(res.data);
    } else {
      fetchDelSelect(ids);
    }
  };

  return (
    <>
      <AmpconList
        pageTitle="Parameter configuration"
        columns={columns}
        dataSource={data?.records || []}
        headerTop={headerTop}
        headerBottom={headerBottom}
        rowSelection={{
          ...rowSelection,
        }}
        handleDelSelect={handleDelSelect}
        pagination={{
          size: 'small',
          total: data?.total || 0,
          current: tableParams.current,
          showTotal: (total) => `Total ${total} items`,
          onChange: (page, pageSize) => {
            setTableParams({
              ...tableParams,
              current: page,
              size: pageSize,
            });
          },
        }}
      ></AmpconList>
      <Modal
        title=" Delete Parameter"
        open={visible}
        onCancel={() => setVisible(false)}
        onOk={() => setVisible(false)}
      >
        <div>
          <p>
            Sorry, this parameter has the following related items and cannot be
            deleted.
          </p>
          <div style={{ height: 380, overflow: 'auto' }}>
            {delData?.map((item: any, index: number) => (
              <div key={index}>
                <p style={{ color: '#ce5025' }}>{item.message}</p>({item.table})
              </div>
            ))}
          </div>
        </div>
      </Modal>
    </>
  );
}

export default index;

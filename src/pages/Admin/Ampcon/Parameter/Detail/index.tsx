import React, { useEffect, useMemo, useState } from 'react';
import FormCom from '@/pages/Licenses/components/FormCom';
import { Button, Form, Input, Select, message } from 'antd';
import { useQuery } from 'react-query';
import {
  getParameterConfigDetailOptions,
  getParameterDetailById,
  postParameterConfigAddForm,
  putParameterConfigDetail,
} from '@/api/ampcon';
import { useNavigate, useParams } from 'react-router-dom';

function index() {
  const router = useNavigate();
  const { id } = useParams();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const { data: optionsData } = useQuery(
    'getFormOptions',
    () => getParameterConfigDetailOptions(),
    {
      select: (res) => res.data,
    }
  );
  const { data } = useQuery(
    ['getParameterDetail', id],
    () => getParameterDetailById(id || ''),
    {
      select: (res) => res.data,
      enabled: !!id,
      onSuccess: (res) => {
        form.setFieldsValue({
          code: res.code,
          name: res.name,
          parameter: res.parameter,
          softwareType: res.softwareType,
        });
      },
    }
  );
  console.log(data);
  const [softType, setSoftType] = useState(0);
  const softwareTypeOptions = useMemo(() => {
    return (
      optionsData?.length > 0 &&
      optionsData[0].list.map((item: any) => (
        <Select.Option key={item.id} value={item.id}>
          {item.name}
        </Select.Option>
      ))
    );
  }, [optionsData]);
  const parameterOptions = useMemo(() => {
    if (optionsData?.length > 0) {
      if (softType === 2) {
        return optionsData[0].children
          .filter((item: any) => item.id === 4)
          .map((item: any) => (
            <Select.Option key={item.id} value={item.id}>
              {item.name}
            </Select.Option>
          ));
      } else {
        return optionsData[0].children.map((item: any) => (
          <Select.Option key={item.id} value={item.id}>
            {item.name}
          </Select.Option>
        ));
      }
    } else {
      return [];
    }
  }, [optionsData, softType]);

  const onFinish = async (values: any) => {
    setLoading(true);
    if (id) {
      const params = {
        id: id,
        code: values.code,
        name: values.name,
      };
      const res = await putParameterConfigDetail(params).finally(() =>
        setLoading(false)
      );
      if (res.code === 200) {
        message.success('Updated successfully', 1).then(() => {
          router(-1);
        });
      } else {
        message.error(res.data.message, 1);
      }
    } else {
      const params = {
        code: values.code,
        name: values.name,
        parameter: values['parameter'].value,
        parameterValue: values['parameter'].label,
        softwareType: values['softwareType'].value,
        softwareTypeValue: values['softwareType'].label,
      };
      const res = await postParameterConfigAddForm(params).finally(() =>
        setLoading(false)
      );
      if (res.code === 200) {
        message.success('Created successfully', 1).then(() => {
          router(-1);
        });
      } else {
        message.error(res.data.message, 1);
      }
    }
  };
  return (
    <FormCom pageTitle="Parameter configuration">
      <Form
        size="large"
        style={{ width: '750px' }}
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 14 }}
        form={form}
        onFinish={onFinish}
      >
        {id ? (
          <>
            <Form.Item label="Software type" name="softwareType">
              <Input disabled />
            </Form.Item>
            <Form.Item label="Parameter" name="parameter">
              <Input disabled />
            </Form.Item>
          </>
        ) : (
          <>
            <Form.Item
              label="Software type"
              name="softwareType"
              rules={[
                { required: true, message: 'Please select software type' },
              ]}
            >
              <Select
                labelInValue
                disabled={!!id}
                onChange={(e) => {
                  form.setFieldsValue({ parameter: undefined });
                  setSoftType(e.value);
                }}
              >
                {softwareTypeOptions}
              </Select>
            </Form.Item>
            <Form.Item
              label="Parameter"
              name="parameter"
              rules={[{ required: true, message: 'Please select parameter' }]}
            >
              <Select labelInValue disabled={!!id}>
                {parameterOptions}
              </Select>
            </Form.Item>
          </>
        )}
        <Form.Item
          label="Name"
          name="name"
          rules={[{ required: true, message: 'Please input name' }]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          label="Code"
          name="code"
          rules={[{ required: true, message: 'Please input code' }]}
        >
          <Input />
        </Form.Item>
        <Form.Item wrapperCol={{ offset: 8, span: 14 }}>
          <Button type="primary" htmlType="submit" loading={loading}>
            Save
          </Button>
        </Form.Item>
      </Form>
    </FormCom>
  );
}

export default index;

import React from 'react';
import styles from '../index.module.scss';
type Props = {
  data: UserEditData;
};
export interface UserEditData {
  hardwareIds: AddDetail[];
  newLicenseName: string;
  oldLicenseName: string;
}
export interface AddDetail {
  expireDate: string;
  hardwareId: string;
}
function UserEdit(props: Props) {
  const { data } = props;
  return (
    <div className={styles.common_wrap}>
      {(data?.newLicenseName || data?.oldLicenseName) && (
        <>
          <div className={styles.line_wrap}>
            <span className={styles.label}>old license name:</span>
            <span className={styles.content}>{data.oldLicenseName}</span>
          </div>
          <div className={styles.line_wrap}>
            <span className={styles.label}>new license name:</span>
            <span className={styles.content}>{data.newLicenseName}</span>
          </div>
        </>
      )}
      {data?.hardwareIds?.length > 0 && (
        <div className={styles.list_wrap}>
          <span className={styles.list_title}>
            Add Hardware ID & Expiration date :
          </span>
          {data.hardwareIds.map((item, index) => (
            <div key={index}>
              {item.hardwareId}
              {item.hardwareId && item.expireDate && ', '}
              {item.expireDate}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

export default UserEdit;

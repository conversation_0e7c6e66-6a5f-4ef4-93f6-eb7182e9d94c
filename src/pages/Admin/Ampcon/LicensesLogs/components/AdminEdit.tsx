import React from 'react';
import styles from '../index.module.scss';
type Props = {
  data: AdminEditData;
};
export interface AdminEditData {
  addDetails: AddDetail[];
  delDetails: DelDetail[];
  editDetails: EditDetail[];
  newComments: string;
  newLicenseName: string;
  oldComments: string;
  oldLicenseName: string;
}

export interface AddDetail {
  expireDate: string;
  hardwareId: string;
}

export interface DelDetail {
  expireDate: string;
  hardwareId: string;
}

export interface EditDetail {
  hasChangeHardwareId: number;
  hasChangeTime: number;
  newExpireDate: string;
  newHardwareId: string;
  oldExpireDate?: string;
  oldHardwareId: string;
}
function AdminEdit(props: Props) {
  const { data } = props;
  return (
    <div className={styles.common_wrap}>
      <div className={styles.line_wrap}>
        <span className={styles.label}>old license name:</span>
        <span className={styles.content}>{data.oldLicenseName}</span>
      </div>
      <div className={styles.line_wrap}>
        <span className={styles.label}>new license name:</span>
        <span className={styles.content}>{data.newLicenseName}</span>
      </div>
      <div className={styles.line_wrap}>
        <span className={styles.label}>old comments:</span>
        <span className={styles.content}>{data.oldComments}</span>
      </div>

      <div className={styles.line_wrap}>
        <span className={styles.label}>new comments:</span>
        <span className={styles.content}>{data.newComments}</span>
      </div>
      {data?.addDetails.length > 0 && (
        <div className={styles.list_wrap}>
          <span className={styles.list_title}>
            Add Hardware ID & Expiration date :
          </span>
          {data.addDetails.map((item, index) => (
            <div key={index}>
              {item.hardwareId}
              {item.hardwareId && item.expireDate && ', '}
              {item.expireDate}
            </div>
          ))}
        </div>
      )}
      {data?.editDetails.length > 0 && (
        <div className={styles.list_wrap}>
          <span className={styles.list_title}>
            Changed Hardware ID & Expiration date :{' '}
          </span>
          {data.editDetails.map((item, index) => (
            <div className={styles.line_wrap} key={index}>
              <span className={styles.label}>Old: </span>
              <span className={styles.content}>
                {item.oldHardwareId}
                {item.oldHardwareId && item.oldExpireDate && ', '}
                {item.oldExpireDate}
              </span>
              <span className={styles.label}>New: </span>
              <span className={styles.content}>
                {item.newHardwareId}
                {item.newHardwareId && item.newExpireDate && ', '}
                {item.newExpireDate}
              </span>
            </div>
          ))}
        </div>
      )}
      {data?.delDetails.length > 0 && (
        <div className={styles.list_wrap}>
          <span className={styles.list_title}>
            Deleted Hardware ID & Expiration date :
          </span>
          {data.delDetails.map((item, index) => (
            <div key={index}>
              {item.hardwareId}
              {item.hardwareId && item.expireDate && ', '}
              {item.expireDate}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

export default AdminEdit;

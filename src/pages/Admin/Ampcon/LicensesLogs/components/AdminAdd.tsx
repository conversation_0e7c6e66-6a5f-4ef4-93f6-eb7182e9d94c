import React from 'react';
import styles from '../index.module.scss';

type Props = {
  data: AdminAddData;
};

type AdminAddData = {
  comments: string;
  deviceId: string;
  expireDate: string;
  hardwareIds: HardwareId[];
  licenseName: string;
  systemMac: string;
};

export interface HardwareId {
  expireDate: string;
  hardwareId: string;
}
function AdminAdd(props: Props) {
  const { data } = props;
  return (
    <div className={styles.common_wrap}>
      <div className={styles.line_wrap}>
        <span className={styles.label}>License Name:</span>
        <span className={styles.content}>{data.licenseName}</span>
      </div>
      <div className={styles.line_wrap}>
        <span className={styles.label}>Comment: </span>
        <span className={styles.content}>{data.comments}</span>
      </div>
      {data?.hardwareIds.length > 0 && (
        <div className={styles.list_wrap}>
          <span className={styles.list_title}>
            Hardware ID & Expiration date :
          </span>
          {data.hardwareIds.map((item, index) => (
            <div key={index}>
              {item.hardwareId}
              {item.hardwareId && item.expireDate && ', '}
              {item.expireDate}
            </div>
          ))}
        </div>
      )}
      {data.systemMac && (
        <div className={styles.line_wrap}>
          <span className={styles.label}>System MAC: </span>
          <span className={styles.content}>{data.systemMac}</span>
        </div>
      )}
      {data.deviceId && (
        <div className={styles.line_wrap}>
          <span className={styles.label}>Device ID: </span>
          <span className={styles.content}>{data.deviceId}</span>
        </div>
      )}
      {data.expireDate && (
        <div className={styles.line_wrap}>
          <span className={styles.label}>Expiration Date: </span>
          <span className={styles.content}>{data.expireDate}</span>
        </div>
      )}
    </div>
  );
}

export default AdminAdd;

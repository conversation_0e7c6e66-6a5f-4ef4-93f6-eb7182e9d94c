import React from 'react';
import styles from '../index.module.scss';
type Props = {
  data: UserRevokeData;
};
type UserRevokeData = {
  hardwareIds: HardwareId[];
  revokeCode: string;
};

export interface HardwareId {
  expireDate: string;
  hardwareId: string;
}

function UserRevoke(props: Props) {
  const { data } = props;
  return (
    <div className={styles.common_wrap}>
      <div className={styles.line_wrap}>
        <span className={styles.label}>Revoke Code:</span>
        <span className={styles.content} style={{ maxWidth: 300 }}>
          {data.revokeCode}
        </span>
      </div>
      {data.hardwareIds.length > 0 && (
        <div className={styles.list_wrap}>
          <span className={styles.list_title}>
            Hardware ID & Expiration date :
          </span>
          {data.hardwareIds.map((item, index) => (
            <div key={index}>
              {item.hardwareId}
              {item.hardwareId && item.expireDate && ', '}
              {item.expireDate}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

export default UserRevoke;

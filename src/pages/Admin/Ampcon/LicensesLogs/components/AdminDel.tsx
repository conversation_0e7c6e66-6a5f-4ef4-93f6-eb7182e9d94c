import React from 'react';
import styles from '../index.module.scss';
type Props = {
  data: AdminDelData;
};
export interface AdminDelData {
  comments: string;
  companyName: string;
  deviceId: string;
  deviceType: string;
  expireDate: string;
  featureType: string;
  hardwareIds: HardwareId[];
  licenseId: number;
  licenseName: string;
  licenseType: string;
  pattern: string;
  softwareType: string;
  softwareVersion: string;
  systemMac: string;
}

export interface HardwareId {
  expireDate: string;
  hardwareId: string;
}
function AdminDel(props: Props) {
  const { data } = props;
  return (
    <div className={styles.common_wrap}>
      <div className={styles.line_wrap}>
        <span className={styles.label}>License ID:</span>
        <span className={styles.content}>{data.licenseId}</span>
      </div>
      <div className={styles.line_wrap}>
        <span className={styles.label}>Company:</span>
        <span className={styles.content}>{data.companyName}</span>
      </div>

      <div className={styles.line_wrap}>
        <span className={styles.label}>Mode:</span>
        <span className={styles.content}>{data.pattern}</span>
      </div>
      <div className={styles.line_wrap}>
        <span className={styles.label}>Software Type:</span>
        <span className={styles.content}>{data.softwareType}</span>
      </div>

      <div className={styles.line_wrap}>
        <span className={styles.label}>Software Version:</span>
        <span className={styles.content}>{data.softwareVersion}</span>
      </div>
      <div className={styles.line_wrap}>
        <span className={styles.label}>Device Type:</span>
        <span className={styles.content}>{data.deviceType}</span>
      </div>
      <div className={styles.line_wrap}>
        <span className={styles.label}>Feature Type:</span>
        <span className={styles.content}>{data.featureType}</span>
      </div>

      <div className={styles.line_wrap}>
        <span className={styles.label}>License Type:</span>
        <span className={styles.content}>{data.licenseType}</span>
      </div>
      <div className={styles.line_wrap}>
        <span className={styles.label}>License Name:</span>
        <span className={styles.content}>{data.licenseName}</span>
      </div>

      {data.systemMac && (
        <div className={styles.line_wrap}>
          <span className={styles.label}>System MAC:</span>
          <span className={styles.content}>{data.systemMac}</span>
        </div>
      )}
      {data.deviceId && (
        <div className={styles.line_wrap}>
          <span className={styles.label}>Device ID:</span>
          <span className={styles.content}>{data.deviceId}</span>
        </div>
      )}
      {data.expireDate && (
        <div className={styles.line_wrap}>
          <span className={styles.label}>Expiration Date:</span>
          <span className={styles.content}>{data.expireDate}</span>
        </div>
      )}

      {data.hardwareIds.length > 0 && (
        <div className={styles.list_wrap}>
          <span className={styles.list_title}>
            Deleted Hardware ID & Expiration date :
          </span>
          {data.hardwareIds.map((item, index) => (
            <div key={index}>
              {item.hardwareId}
              {item.hardwareId && item.expireDate && ', '}
              {item.expireDate}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

export default AdminDel;

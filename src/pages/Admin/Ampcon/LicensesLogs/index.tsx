import React, { useState } from 'react';
import AmpconList from '../../components/AmpconList';
import { useQuery } from 'react-query';
import {
  getAmpconLogsDetail,
  getAmpconLogsList,
  getAmpconLogsListFilter,
} from '@/api/ampcon';
import { ColumnProps } from 'antd/es/table';
import TableHead from './TableHead';
import { Modal, ConfigProvider, theme, Spin } from 'antd';
import styles from './index.module.scss';
import AdminEdit from './components/AdminEdit';
import AdminDel from './components/AdminDel';
import AdminAdd from './components/AdminAdd';
import UserEdit from './components/UserEdit';
import UserRevoke from './components/UserRevoke';

type DataType = {
  id: number;
  operationType: string;
  operator: string;
  operationTime: string;
  licenseId: number;
  company: string;
  softwareType: string;
  softwareVersion: string;
  deviceType: string;
  featureType: string;
};
function index() {
  const [tableParams, setTableParams] = useState({
    size: 10,
    current: 1,
  });
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [detailData, setDetailData] = useState<any>();
  const { data } = useQuery(
    ['authLogs', tableParams],
    () => getAmpconLogsList(tableParams),
    {
      select: (res) => res.data,
    }
  );
  const { data: filterData } = useQuery(
    'ampconLogsFilterData',
    () => getAmpconLogsListFilter(),
    {
      select: (res) => res.data,
    }
  );
  console.log(filterData);
  const columns: ColumnProps<DataType>[] = [
    {
      title: 'Log id',
      dataIndex: 'id',
    },
    {
      title: 'Operation type',
      dataIndex: 'operationType',
    },
    {
      title: 'Operator',
      dataIndex: 'operator',
    },
    {
      title: 'Operation time',
      dataIndex: 'operationTime',
    },
    {
      title: 'License id',
      dataIndex: 'licenseId',
    },
    {
      title: 'Company',
      dataIndex: 'company',
    },
    {
      title: 'Software type',
      dataIndex: 'softwareType',
    },
    {
      title: 'Software version',
      dataIndex: 'softwareVersion',
    },
    {
      title: 'Device type',
      dataIndex: 'deviceType',
    },
    {
      title: 'Feature type',
      dataIndex: 'featureType',
    },
    {
      title: 'License Type',
      dataIndex: 'licenseType',
    },
    {
      title: 'Action',
      key: 'action',
      render: (text, record) => {
        return (
          <a onClick={() => handleOpenDetailModal(record.id)}>See Details</a>
        );
      },
    },
  ];
  const onValuesChange = (changed: any, allValues: any) => {
    let expireTime = undefined;
    let operationTime = undefined;
    if (Array.isArray(allValues.operationTime)) {
      operationTime = [
        allValues.operationTime[0].format('YYYY-MM-DD HH:mm:ss'),
        allValues.operationTime[1].format('YYYY-MM-DD HH:mm:ss'),
      ];
    }
    if (Array.isArray(allValues.expireTime)) {
      expireTime = [
        allValues.expireTime[0].format('YYYY-MM-DD'),
        allValues.expireTime[1].format('YYYY-MM-DD'),
      ];
    }
    setTableParams({ ...tableParams, ...allValues, expireTime, operationTime });
  };
  const handleOpenDetailModal = (id: number) => {
    setDetailModalVisible(true);
    fetchDetail(id);
  };
  const fetchDetail = async (id: number) => {
    setLoading(true);
    const res = await getAmpconLogsDetail(id).finally(() => setLoading(false));
    console.log(res.data);
    setDetailData(res.data);
  };
  return (
    <>
      <AmpconList
        pageTitle="AmpCon licenses operation logs"
        columns={columns}
        dataSource={data?.records || []}
        headerTop={
          <TableHead onValuesChange={onValuesChange} data={filterData} />
        }
        pagination={{
          size: 'small',
          total: data?.total || 0,
          current: tableParams.current,
          showTotal: (total) => `Total ${total} items`,
          onChange: (page, pageSize) => {
            setTableParams({
              ...tableParams,
              current: page,
              size: pageSize,
            });
          },
        }}
      ></AmpconList>
      <ConfigProvider
        theme={{
          algorithm: [theme.defaultAlgorithm],
        }}
      >
        <Modal
          title="Operation log"
          open={detailModalVisible}
          onCancel={() => setDetailModalVisible(false)}
          onOk={() => setDetailModalVisible(false)}
          getContainer={false}
        >
          <Spin spinning={loading}>
            <div className={styles.modal_content}>
              <div className={styles.title}>
                <div className={styles.line}>
                  <span>{detailData?.operator}</span>
                  <span>{detailData?.operationTime}</span>
                </div>
                <div className={styles.line}>
                  <span>{detailData?.operationType}</span>
                  <span>{detailData?.id}</span>
                </div>
              </div>
              <div className={styles.content}>
                {detailData?.adminEdit && (
                  <AdminEdit data={detailData.adminEdit} />
                )}
                {detailData?.adminDelete && (
                  <AdminDel data={detailData.adminDelete} />
                )}
                {(detailData?.adminAdd || detailData?.userAdd) && (
                  <AdminAdd data={detailData.adminAdd || detailData.userAdd} />
                )}
                {(detailData?.userEdit || detailData?.editLicenseName) && (
                  <UserEdit
                    data={detailData.userEdit || detailData.editLicenseName}
                  />
                )}
                {detailData?.revokeCode && (
                  <UserRevoke data={detailData.revokeCode} />
                )}
              </div>
            </div>
          </Spin>
        </Modal>
      </ConfigProvider>
    </>
  );
}

export default index;

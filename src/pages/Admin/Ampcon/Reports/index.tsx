import React, { useState } from 'react';
import FormCom from '@/pages/Licenses/components/FormCom';
import { Form, Select, DatePicker, Button } from 'antd';
import { useQuery } from 'react-query';
import {
  getAmpconReportFilter,
  postAmpconReportExcelDownload,
} from '@/api/ampcon';
import { downloadByBlob } from '@/utils/utils';

function index() {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const { data: filter } = useQuery(
    'ampcon_report_filter',
    () => getAmpconReportFilter(),
    {
      select: (res) => res.data,
    }
  );
  const handleSubmit = async (values: any) => {
    const params = {
      ...values,
    };
    if (values['dateList'] && values['dateList'].length > 0) {
      params['dateList'] = values['dateList'].map((item: any) => {
        return item.format('YYYY-MM-DD HH:mm:ss');
      });
    }
    setLoading(true);
    const res = await postAmpconReportExcelDownload(params).finally(() =>
      setLoading(false)
    );
    downloadByBlob(res);
  };

  return (
    <FormCom pageTitle="AmpCon license reporting">
      <Form
        form={form}
        style={{ width: '860px' }}
        size="large"
        onFinish={handleSubmit}
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 16 }}
      >
        <Form.Item
          name="companyId"
          label="Company"
          rules={[{ required: true, message: 'Please input company!' }]}
        >
          <Select
            showSearch
            filterOption={(input, option?: { children: string }) => {
              return (option?.children ?? '')
                .toLowerCase()
                .includes(input.toLowerCase());
            }}
          >
            {filter?.companyList.map((item: any) => (
              <Select.Option key={item.id} value={item.id}>
                {item.name}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item name="softwareType" label="Software type">
          <Select allowClear>
            {filter?.softwareTypeList.map((item: any) => (
              <Select.Option key={item.id} value={item.id}>
                {item.name}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item
          name="actionType"
          label="Action type"
          rules={[{ required: true, message: 'Please input action type!' }]}
        >
          <Select>
            {filter?.actionTypeList.map((item: any) => (
              <Select.Option key={item.value} value={item.value}>
                {item.label}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item
          name="dateList"
          label="Create date"
          rules={[{ required: true, message: 'Please input create date!' }]}
        >
          <DatePicker.RangePicker></DatePicker.RangePicker>
        </Form.Item>
        <Form.Item wrapperCol={{ offset: 8, span: 16 }}>
          <Button type="primary" htmlType="submit" loading={loading}>
            Generate
          </Button>
        </Form.Item>
      </Form>
    </FormCom>
  );
}

export default index;

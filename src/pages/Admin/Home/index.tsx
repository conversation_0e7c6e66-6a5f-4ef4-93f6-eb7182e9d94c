import styles from './index.module.scss';
import { queryAdminList, queryRecentActions } from '@/api/auth';
import { useQuery } from 'react-query';
import { AdminMenuType } from '@/api/interface';
import {
  CloseOutlined,
  EditOutlined,
  PlusSquareOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import useSetGlobalLoading from '@/hooks/useSetGlobalLoading';
import { pathMap } from '@/utils/pathMap';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { selectPermission, setPermission } from '@/store/feature/userSlice';

interface ActionRecordType {
  actionFlag: number;
  contentTypeId: number;
  contentTypeName: string;
  objectId: string;
  objectRepr: string;
}
function Home() {
  const dispatch = useAppDispatch();
  const permission = useAppSelector(selectPermission);
  const router = useNavigate();
  const { data, isLoading } = useQuery('adminList', () => queryAdminList(), {
    select: (res) => res.data as AdminMenuType[],
    onSuccess: (res) => {
      dispatch(setPermission(res));
    },
  });
  useSetGlobalLoading(isLoading);
  const { data: actionRecordData, isLoading: actionRecordLoading } = useQuery(
    'actionRecord',
    () => queryRecentActions(),
    {
      select: (res) => res.data as ActionRecordType[],
    }
  );
  const jumpTo = (route: string) => () => {
    router(route);
  };
  const goDetail = (item: ActionRecordType) => {
    if (item.actionFlag === 3) return;
    // 如果item中的contentTypeId与pathMap中的key匹配，则跳转到对应的页面
    if (Object.keys(pathMap).includes(item.contentTypeId.toString())) {
      router(`${pathMap[item.contentTypeId]}/${item.objectId}`);
    }
  };
  function renderAdminList(list: AdminMenuType[]) {
    return list.map((item) => {
      return (
        <div className={styles.part_wrap} key={item.title}>
          <div className={styles.list_row_title}>{item.title}</div>
          <div className={styles.list_row_child}>
            {item.menuList.map((child) => {
              return (
                <div key={child.label} className={styles.list_row_child_item}>
                  {child.permission.hasChange ? (
                    <span
                      onClick={jumpTo(child.route)}
                      className={styles.label}
                    >
                      {child.label}
                    </span>
                  ) : (
                    <span className={styles.label_black}>{child.label}</span>
                  )}
                  {/* <span onClick={jumpTo(child.route)} className={styles.label}>
                    {child.label}
                  </span> */}
                  <div className={styles.btn_wrap}>
                    {child.rightList.map((btn) => {
                      if (btn.label.includes('add')) {
                        return (
                          <div
                            key={btn.label}
                            className={styles.btn}
                            onClick={jumpTo(btn.route)}
                          >
                            <PlusSquareOutlined style={{ color: '#8c8c8c' }} />
                            <span>Add</span>
                          </div>
                        );
                      } else if (btn.label.includes('change')) {
                        return (
                          <div
                            key={btn.label}
                            className={styles.btn}
                            onClick={jumpTo(btn.route)}
                          >
                            <EditOutlined style={{ color: '#8c8c8c' }} />
                            <span>Change</span>
                          </div>
                        );
                      }
                    })}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      );
    });
  }
  return (
    <div className={styles.admin_home_wrap}>
      <h1 className={styles.title}>Admin panel</h1>
      <main className={styles.content}>
        <div className={styles.list_wrap}>
          {renderAdminList(permission || [])}
        </div>
        <div className={styles.action_record_box}>
          <div className={styles.title}>Recent Actions</div>
          <div className={styles.content_wrap}>
            <div className={styles.title}>My Actions</div>
            {actionRecordData?.map((item, index) => (
              <div className={styles.record_item} key={index}>
                <div
                  className={styles.record_item_title}
                  onClick={() => goDetail(item)}
                >
                  {item.actionFlag === 3 && (
                    <CloseOutlined
                      style={{
                        color: '#FF0003',
                        fontSize: '16px',
                        marginRight: '6px',
                      }}
                    />
                  )}
                  {item.objectRepr}
                </div>
                <div className={styles.record_item_content}>
                  {item.contentTypeName}
                </div>
              </div>
            ))}
          </div>
        </div>
      </main>
    </div>
  );
}

export default Home;

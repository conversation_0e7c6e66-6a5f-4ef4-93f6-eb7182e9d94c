.admin_home_wrap {
  flex: 1;
  background-color: $bgColorGrey;
  .title {
    color: $colorPrimary;
    font-size: 35px;
    padding: 50px 0;
    text-align: center;
  }
  .content {
    margin: 0 auto;
    width: 80%;
    display: flex;
    gap: 50px;
    .list_wrap {
      width: 75%;
      font-size: 12px;
      background-color: #fff;
      .part_wrap {
        .list_row_title {
          padding: 12px;
          color: #1c1f21;
          background-color: #ececec;
        }
        .list_row_child {
          color: $colorPrimary;
          .list_row_child_item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px 25px;
            border-bottom: 1px solid #ececec;
            &:last-child {
              border-bottom: none;
            }
            .label {
              cursor: pointer;
            }
            .label_black {
              color: #000;
            }
            .btn_wrap {
              display: flex;
              gap: 10px;
              .btn {
                cursor: pointer;
                span {
                  margin-right: 8px;
                }
              }
            }
            span {
              &:hover {
                color: #ffa67d;
              }
            }
          }
        }
      }
    }
    .action_record_box {
      flex: 1;

      .title {
        padding: 12px;
        font-size: 12px;
        color: #1c1f21;
        background-color: #ececec;
        text-align: start;
      }
      .content_wrap {
        padding: 12px;
        background-color: #fff;
        .title {
          padding: 0;
          font-size: 14px;
          color: #666;
          background-color: inherit;
        }
        .record_item {
          font-size: 12px;
          padding: 8px 0;
          .record_item_title {
            cursor: pointer;
            color: $colorPrimary;
          }
          .record_item_content {
            color: #999;
          }
        }
      }
    }
  }
}

import { ChangePasswordParams } from '@/api/interface';
import { postChangePassword } from '@/api/user/login';
import FormCom from '@/pages/Licenses/components/FormCom';
import { Button, Form, Input, message } from 'antd';
import { useNavigate } from 'react-router-dom';

function PasswordChange() {
  const [form] = Form.useForm();
  const router = useNavigate();
  const onFinish = async (values: ChangePasswordParams) => {
    const data = await postChangePassword(values);
    if (data.code === 200) {
      message
        .success('Password changed successfully', 2)
        .then(() => router(-1));
    }
  };
  return (
    <FormCom pageTitle="Password change">
      <div style={{ marginBottom: '50px' }}>
        Please enter your old password, for security's sake, and then enter your
        new password twice so we can verify you typed it in correctly.
      </div>
      <Form
        autoComplete="off"
        style={{ minWidth: 350 }}
        layout="vertical"
        onFinish={onFinish}
        size="large"
        form={form}
      >
        <Form.Item
          label="Old Password"
          name="oldPassword"
          rules={[{ required: true }]}
        >
          <Input.Password />
        </Form.Item>
        <Form.Item
          label="Password"
          name="password"
          rules={[{ required: true }]}
        >
          <Input.Password />
        </Form.Item>
        <Form.Item
          label="Confirm Password"
          name="passwordConfirm"
          dependencies={['password']}
          rules={[
            {
              required: true,
            },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue('password') === value) {
                  return Promise.resolve();
                }
                return Promise.reject(
                  new Error('The new password that you entered do not match!')
                );
              },
            }),
          ]}
        >
          <Input.Password />
        </Form.Item>
        <Form.Item>
          <Button
            type="primary"
            htmlType="submit"
            style={{ width: '100%', marginTop: '10px' }}
          >
            Change my password
          </Button>
        </Form.Item>
      </Form>
    </FormCom>
  );
}

export default PasswordChange;

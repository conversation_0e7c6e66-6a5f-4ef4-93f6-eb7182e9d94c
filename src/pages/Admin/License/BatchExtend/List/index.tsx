import { useState } from 'react';
import CommonList from '@/pages/Admin/components/CommonList';
import { useQuery } from 'react-query';
import { getLicenseBatchExtend } from '@/api/admin-license';
import { ColumnsType } from 'antd/es/table';
import { Link, useNavigate } from 'react-router-dom';
import { Button } from 'antd';
import usePermission from '@/hooks/usePermission';
import useBreadcrumb from '@/hooks/useBreadcrumb';

type DataType = {
  id: number;
  logs: string;
};

function BatchExtendList() {
  const router = useNavigate();
  const breadcrumb = useBreadcrumb();
  const { hasAdd } = usePermission();
  const [tableParams, setTableParams] = useState({
    current: 1,
    size: 10,
  });
  const { data } = useQuery(
    ['licenseBatchExtendLogs', tableParams],
    () => getLicenseBatchExtend(tableParams),
    {
      select: (res) => res.data,
    }
  );
  const columns: ColumnsType<DataType> = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <Link to={`${record.id}`} onClick={() => breadcrumb(record.logs)}>
          {text}
        </Link>
      ),
    },
    {
      title: 'Mode types',
      dataIndex: 'modeTypes',
    },
  ];
  return (
    <>
      <CommonList
        pageTitle="Extend expiration date of batch licenses"
        dataSource={data?.records || []}
        columns={columns}
        showSearch={false}
        pagination={{
          size: 'small',
          total: data?.total || 0,
          showTotal: (total) => `Total ${total} items`,
          onChange: (page, pageSize) => {
            setTableParams({
              ...tableParams,
              current: page,
              size: pageSize,
            });
          },
        }}
      >
        {hasAdd && (
          <Button type="primary" onClick={() => router('add')}>
            Add extend expiration date of batch license
          </Button>
        )}
      </CommonList>
    </>
  );
}

export default BatchExtendList;

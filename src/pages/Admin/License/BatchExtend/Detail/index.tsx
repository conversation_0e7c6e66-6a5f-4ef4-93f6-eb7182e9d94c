import { useRef, useState } from 'react';
import { Button, DatePicker, Form, Input, Select, message } from 'antd';
import { useQuery } from 'react-query';
import { useNavigate, useParams } from 'react-router-dom';
import {
  getExpirationDate,
  getLicenseBatchExtendDetail,
  getLicenseBatchExtendForm,
  postLicenseBatchExtendForm,
  postLicenseBatchExtendUpdate,
} from '@/api/admin-license';
import useSetGlobalLoading from '@/hooks/useSetGlobalLoading';
import FormCom from '@/pages/Licenses/components/FormCom';
import dayjs from 'dayjs';

function BatchExtendDetail() {
  const router = useNavigate();
  const [form] = Form.useForm();
  const { id } = useParams();
  const fetchParams = useRef({} as any);
  const [expirationDateList, setExpirationDateList] = useState<string[]>([]);
  // const { isLoading } = useQuery(
  //   ['LicenseBatchExtendDetail', id],
  //   () => getLicenseBatchExtendDetail(id || ''),
  //   {
  //     enabled: !!id,
  //     select: (res) => res.data,
  //     onSuccess: (data) => {
  //       data.expirationDate = dayjs(data.expirationDate);
  //       data.expireDate = dayjs(data.expireDate);
  //       form.setFieldsValue(data);
  //     },
  //   }
  // );
  const { data: optionData, isLoading } = useQuery(
    ['LicenseBatchExtendDetailOptions'],
    () => getLicenseBatchExtendForm(),
    {
      select: (res) => res.data,
    }
  );
  useSetGlobalLoading(isLoading);

  const onFinish = async (value: any) => {
    const params = {
      ...value,
      expireDate: value.expireDate?.format('YYYY-MM-DD'),
    };
    if (id) {
      params.id = id;
      const data = await postLicenseBatchExtendUpdate(params);
      if (data) {
        message.success('Update successfully', 1).then(() => {
          router(-1);
        });
      }
    } else {
      const data = await postLicenseBatchExtendForm(params);
      if (data) {
        message.success('Created successfully', 1).then(() => {
          router(-1);
        });
      }
    }
  };
  const onValueChange = async (changedValues: any) => {
    const fieldList = ['companyId', 'mode', 'speedTypeId', 'featureTypeId'];
    const isChange = fieldList.some((item) => {
      return changedValues[item];
    });
    if (!isChange) {
      return;
    }
    //当变化的字段在fieldList中时，就将字段和值存储到一个对象中
    fieldList.forEach((item) => {
      if (changedValues[item]) {
        fetchParams.current[item] = changedValues[item];
      }
    });
    const data = await getExpirationDate(fetchParams.current);
    setExpirationDateList(data.data.expirationDateList);
  };
  return (
    <FormCom pageTitle="Extend expiration date of batch licenses">
      <Form
        style={{ width: '600px' }}
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 14 }}
        form={form}
        onFinish={onFinish}
        onValuesChange={onValueChange}
      >
        <div
          style={{
            textAlign: 'center',
            paddingBottom: '20px',
            fontSize: '12px',
          }}
        >
          Admins can use this screen to extend the license expiration date at
          batch level. All the licenses that match the filters (Company, Mode,
          Speed Type, Feature Type, Expiration Date) will be extended. To view
          the extension logs, go to the home page and select the "Batch extend
          logs" option.
        </div>
        <Form.Item
          label="Company"
          name="companyId"
          rules={[{ required: true, message: 'Please select company' }]}
        >
          <Select
            options={optionData?.companyList || []}
            fieldNames={{ label: 'name', value: 'id' }}
            showSearch
            filterOption={(input, option) =>
              (option?.name ?? '').toLowerCase().includes(input.toLowerCase())
            }
          ></Select>
        </Form.Item>
        <Form.Item
          label="Mode"
          name="mode"
          rules={[{ required: true, message: 'Please select mode' }]}
        >
          <Select>
            {optionData?.modeList?.map((item: any) => (
              <Select.Option key={item} value={item}>
                {item}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item
          label="Speed type"
          name="speedTypeId"
          rules={[{ required: true, message: 'Please select speed type' }]}
        >
          <Select
            options={optionData?.speedTypeList || []}
            fieldNames={{ label: 'name', value: 'id' }}
          ></Select>
        </Form.Item>
        <Form.Item
          label="Feature type"
          name="featureTypeId"
          rules={[{ required: true, message: 'Please select feature type' }]}
        >
          <Select
            options={optionData?.featureTypeList || []}
            fieldNames={{ label: 'name', value: 'id' }}
          ></Select>
        </Form.Item>
        <Form.Item
          label="Expiration date"
          name="expirationDate"
          rules={[{ required: true, message: 'Please select expiration date' }]}
        >
          <Select>
            {expirationDateList.map((v) => (
              <Select.Option key={v} value={v}>
                {v}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item
          label="New expiration date"
          name="expireDate"
          rules={[
            { required: true, message: 'Please select new expiration date' },
          ]}
        >
          <DatePicker />
        </Form.Item>
        <Form.Item
          label="Comment"
          name="comments"
          rules={[{ required: true, message: 'Please input comment' }]}
        >
          <Input.TextArea maxLength={255} />
        </Form.Item>
        <Form.Item wrapperCol={{ offset: 8, span: 14 }}>
          <Button type="primary" htmlType="submit">
            Save
          </Button>
        </Form.Item>
      </Form>
    </FormCom>
  );
}

export default BatchExtendDetail;

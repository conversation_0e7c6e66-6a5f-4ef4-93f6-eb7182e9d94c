import { useState } from 'react';
import CommonList from '@/pages/Admin/components/CommonList';
import { useQuery } from 'react-query';
import { getLicenseSummaryDetailList } from '@/api/admin-license';
import { ColumnsType, TableProps } from 'antd/es/table';
import { Link } from 'react-router-dom';
import useBreadcrumb from '@/hooks/useBreadcrumb';

type DataType = {
  id: number;
  company: string;
  mode: string;
  speedType: string;
  featureType: string;
  remaining: number;
  expirationDate: string;
};

type TableParams = {
  current: number;
  size: number;
  search: string;
  sortList: { column: string; order: string }[];
};
function SummaryDetailList() {
  const breadcrumb = useBreadcrumb();
  const [tableParams, setTableParams] = useState<TableParams>({
    current: 1,
    size: 10,
    search: '',
    sortList: [],
  });
  const { data, isLoading } = useQuery(
    ['summaryDetailList', tableParams],
    () => getLicenseSummaryDetailList(tableParams),
    {
      select: (res) => res.data,
    }
  );
  const columns: ColumnsType<DataType> = [
    {
      title: 'Company',
      dataIndex: 'company',
      key: 'company',
      render: (text, record) => (
        <Link
          to={`${record.id}`}
          onClick={() =>
            breadcrumb(
              `${record.company}-${record.mode}-${record.speedType}-${record.featureType}-${record.remaining}-${record.expirationDate}`
            )
          }
        >
          {text}
        </Link>
      ),
      sorter: {
        multiple: 1,
      },
    },
    {
      title: 'Mode',
      dataIndex: 'mode',
      key: 'mode',
      sorter: {
        multiple: 2,
      },
    },
    {
      title: 'Speed type',
      dataIndex: 'speedType',
      sorter: {
        multiple: 3,
      },
    },
    {
      title: 'Feature type',
      dataIndex: 'featureType',
      sorter: {
        multiple: 4,
      },
    },
    {
      title: 'Available Licenses',
      dataIndex: 'remaining',
      sorter: {
        multiple: 5,
      },
    },
    {
      title: 'Expiration date',
      dataIndex: 'expirationDate',
      sorter: {
        multiple: 6,
      },
    },
    {
      title: 'Assigned Licenses',
      dataIndex: 'assigned',
      sorter: {
        multiple: 7,
      },
    },
  ];

  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    const sortList = [];
    if (sorter instanceof Array) {
      sorter.forEach((item) => {
        sortList.push({
          column: item.field,
          order: item.order === 'ascend' ? 'ASC' : 'DESC',
        });
      });
    } else {
      if (sorter.order) {
        sortList.push({
          column: sorter.field,
          order: sorter.order === 'ascend' ? 'ASC' : 'DESC',
        });
      }
    }
    setTableParams({
      ...tableParams,
      current: pagination.current,
      size: pagination.pageSize,
      sortList,
    });
  };
  return (
    <>
      <CommonList
        pageTitle="License summary details"
        loading={isLoading}
        dataSource={data?.records || []}
        columns={columns}
        showAddBtn={false}
        handleSearch={(value: string) => {
          setTableParams({
            ...tableParams,
            current: 1,
            search: value,
          });
        }}
        pagination={{
          size: 'small',
          total: data?.total || 0,
          current: tableParams.current,
          showTotal: (total) => `Total ${total} items`,
          // onChange: (page, pageSize) => {
          //   console.log(page, pageSize);
          //   setTableParams({
          //     ...tableParams,
          //     current: page,
          //     size: pageSize,
          //   });
          // },
        }}
        onChange={handleTableChange}
      ></CommonList>
    </>
  );
}

export default SummaryDetailList;

import { useState } from 'react';
import CommonList from '@/pages/Admin/components/CommonList';
import { useQuery } from 'react-query';
import { getLicenseLimitationLogs } from '@/api/admin-license';
import { ColumnsType } from 'antd/es/table';
import { Link } from 'react-router-dom';
import useBreadcrumb from '@/hooks/useBreadcrumb';

type DataType = {
  id: number;
  logs: string;
};

function LimitationLogsList() {
  const [tableParams, setTableParams] = useState({
    current: 1,
    size: 10,
  });
  const breadcrumb = useBreadcrumb();
  const { data } = useQuery(
    ['limitationLogsList', tableParams],
    () => getLicenseLimitationLogs(tableParams),
    {
      select: (res) => res.data,
    }
  );
  const columns: ColumnsType<DataType> = [
    {
      title: 'Logs',
      dataIndex: 'logs',
      key: 'logs',
      render: (text, record) => (
        <Link to={`${record.id}`} onClick={() => breadcrumb(record.logs)}>
          {text}
        </Link>
      ),
    },
  ];
  return (
    <>
      <CommonList
        pageTitle="License limitation logs"
        dataSource={data?.records || []}
        columns={columns}
        showAddBtn={false}
        showSearch={false}
        pagination={{
          size: 'small',
          total: data?.total || 0,
          showTotal: (total) => `Total ${total} items`,
          onChange: (page, pageSize) => {
            setTableParams({
              ...tableParams,
              current: page,
              size: pageSize,
            });
          },
        }}
      ></CommonList>
    </>
  );
}

export default LimitationLogsList;

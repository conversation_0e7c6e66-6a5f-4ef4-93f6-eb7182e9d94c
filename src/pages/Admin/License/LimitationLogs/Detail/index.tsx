import { Form, Input } from 'antd';
import { useQuery } from 'react-query';
import { useParams } from 'react-router-dom';
import { getLicenseLimitationLogsDetail } from '@/api/admin-license';
import useSetGlobalLoading from '@/hooks/useSetGlobalLoading';
import FormCom from '@/pages/Licenses/components/FormCom';

function LimitationLogsDetail() {
  const [form] = Form.useForm();
  const { id } = useParams();
  const { isLoading } = useQuery(
    ['limitationLogsDetail', id],
    () => getLicenseLimitationLogsDetail(id || ''),
    {
      select: (res) => res.data,
      onSuccess: (data) => {
        form.setFieldsValue(data);
      },
    }
  );
  useSetGlobalLoading(isLoading);
  return (
    <FormCom pageTitle="License limitation logs">
      <Form
        style={{ width: '600px' }}
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 14 }}
        disabled
        form={form}
      >
        <Form.Item label="Created" name="created">
          <Input />
        </Form.Item>
        <Form.Item label="Company" name="company">
          <Input />
        </Form.Item>
        <Form.Item label="User" name="user">
          <Input />
        </Form.Item>
        <Form.Item label="Mode" name="mode">
          <Input />
        </Form.Item>
        <Form.Item label="Speed type" name="speedType">
          <Input />
        </Form.Item>
        <Form.Item label="Feature type" name="featureType">
          <Input />
        </Form.Item>
        <Form.Item label="Changed field" name="changedField">
          <Input />
        </Form.Item>
        <Form.Item label="Old value" name="oldValue">
          <Input />
        </Form.Item>
        <Form.Item label="New value" name="newValue">
          <Input />
        </Form.Item>
        <Form.Item label="Comment" name="comment">
          <Input />
        </Form.Item>
      </Form>
    </FormCom>
  );
}

export default LimitationLogsDetail;

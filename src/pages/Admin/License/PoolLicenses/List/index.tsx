import { useRef, useState } from 'react';
import CommonList from '@/pages/Admin/components/CommonList';
import { useQuery } from 'react-query';
import {
  delAdminPoolLicenseByIds,
  getAdminPoolLicenseList,
  putAdminPoolLicenseRenewedByIds,
} from '@/api/admin-license';
import { ColumnsType } from 'antd/es/table';
import { Link, useNavigate } from 'react-router-dom';
import { Button, Space, message, Popconfirm } from 'antd';
import { CheckCircleFilled, MinusCircleFilled } from '@ant-design/icons';
import useBreadcrumb from '@/hooks/useBreadcrumb';

type DataType = {
  companyName: string;
  createTime: string;
  decommissioned: number;
  expirationDate: string;
  featureType: string;
  hardwareId: string;
  id: number;
  mode: string;
  speedType: string;
  status: string;
};

type TableParams = {
  current: number;
  size: number;
  search: string;
  sortList: { column: string; order: string }[];
};
function AdminPoolLicensesList() {
  const router = useNavigate();
  const breadcrumb = useBreadcrumb();
  const [tableParams, setTableParams] = useState<TableParams>({
    current: 1,
    size: 10,
    search: '',
    sortList: [],
  });
  const [selectedKeys, setSelectedKeys] = useState<number[]>([]);
  const { data, refetch } = useQuery(
    ['AdminPoolLicensesList', tableParams],
    () => getAdminPoolLicenseList(tableParams),
    {
      select: (res) => res.data,
    }
  );

  const columns: ColumnsType<DataType> = [
    {
      title: 'Hardware ID',
      dataIndex: 'hardwareId',
      render: (text, record) => (
        <Link
          to={`${record.id}`}
          onClick={() =>
            breadcrumb(
              `${record.companyName}-${record.mode}-${record.speedType}-${record.featureType}(${record.createTime})`
            )
          }
        >
          {text ? text : '(None)'}
        </Link>
      ),
      sorter: {
        multiple: 1,
      },
    },
    {
      title: 'Create time',
      dataIndex: 'createTime',
      sorter: {
        multiple: 2,
      },
    },
    {
      title: 'Expire date',
      dataIndex: 'expirationDate',
      sorter: {
        multiple: 3,
      },
    },
    {
      title: 'Company',
      dataIndex: 'companyName',
      sorter: {
        multiple: 4,
      },
    },
    {
      title: 'Status',
      dataIndex: 'status',
      sorter: {
        multiple: 5,
      },
    },
    {
      title: 'Speed type',
      dataIndex: 'speedType',
      sorter: {
        multiple: 6,
      },
    },
    {
      title: 'Feature type',
      dataIndex: 'featureType',
      sorter: {
        multiple: 7,
      },
    },
    {
      title: 'Mode',
      dataIndex: 'mode',
      sorter: {
        multiple: 8,
      },
    },
    {
      title: 'Decommissioned',
      dataIndex: 'decommissioned',
      align: 'center',
      render: (text, record) =>
        record.decommissioned === 0 ? (
          <CheckCircleFilled style={{ color: '#2FC44D' }} />
        ) : (
          <MinusCircleFilled style={{ color: '#F5222D' }} />
        ),
      sorter: {
        multiple: 9,
      },
    },
    {
      title: 'Action',
      key: 'action',
      render: (text, record) => (
        <Space>
          <Popconfirm
            title="Are you sure to delete this item?"
            onConfirm={() => delItem(record.id)}
          >
            <a>Delete</a>
          </Popconfirm>
          <a onClick={() => renewedItem(record.id)}>Renewed</a>
        </Space>
      ),
    },
  ];
  const rowSelection = {
    // onChange: (selectedRowKeys: React.Key[]) => {
    //   selectedKeys.current = selectedRowKeys;
    // },
    selectedRowKeys: selectedKeys,
    onSelect: (record: any, selected: boolean) => {
      if (selected) {
        setSelectedKeys((before) => {
          return [...before, record.id];
        });
      } else {
        setSelectedKeys((before) => {
          return before.filter((el) => el !== record.id);
        });
      }
    },
    onSelectAll: (selected: boolean, selectedRows: any, changeRows: any) => {
      if (selected) {
        setSelectedKeys((before) => {
          const ids = changeRows.map((el: any) => el.id);
          return [...before, ...ids];
        });
      } else {
        const ids = changeRows.map((el: any) => el.id);
        setSelectedKeys((before) => {
          return before.filter((el) => ids.indexOf(el) == -1);
        });
      }
    },
  };
  const delItem = (id: number) => {
    fetchDelSelect(id);
  };
  const renewedItem = (id: number) => {
    fetchRenewedSelect(id);
  };
  const fetchDelSelect = async (id = 0) => {
    const idOrIds = id ? [id] : selectedKeys;
    const data = await delAdminPoolLicenseByIds(idOrIds);
    if (data) {
      message.success('Delete Success!', 2);
      refetch();
      setSelectedKeys([]);
    }
  };
  const fetchRenewedSelect = async (id = 0) => {
    const idOrIds = id ? [id] : selectedKeys;
    const data = await putAdminPoolLicenseRenewedByIds(idOrIds);
    if (data) {
      message.success('Renewed Success!', 2);
      refetch();
      setSelectedKeys([]);
    }
  };
  const handleSearch = (value: string) => {
    setTableParams({
      ...tableParams,
      current: 1,
      search: value,
    });
  };
  const deleteBatch = () => {
    if (selectedKeys.length > 0) {
      fetchDelSelect();
    } else {
      message.warning('Please select at least one item!', 2);
    }
  };
  const renewedBatch = () => {
    if (selectedKeys.length > 0) {
      fetchRenewedSelect();
    } else {
      message.warning('Please select at least one item!', 2);
    }
  };
  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    const sortList = [];
    if (sorter instanceof Array) {
      sorter.forEach((item) => {
        sortList.push({
          column: item.field,
          order: item.order === 'ascend' ? 'ASC' : 'DESC',
        });
      });
    } else {
      if (sorter.order) {
        sortList.push({
          column: sorter.field,
          order: sorter.order === 'ascend' ? 'ASC' : 'DESC',
        });
      }
    }
    setTableParams({
      ...tableParams,
      current: pagination.current,
      size: pagination.pageSize,
      sortList,
    });
  };
  return (
    <>
      <CommonList
        pageTitle="Admin Pool Licenses"
        dataSource={data?.records || []}
        columns={columns}
        handleSearch={handleSearch}
        rowSelection={{
          ...rowSelection,
        }}
        onChange={handleTableChange}
        pagination={{
          size: 'small',
          total: data?.total || 0,
          current: tableParams.current,
          showTotal: (total) => `Total ${total} items`,
          // onChange: (page, pageSize) => {
          //   setTableParams({
          //     ...tableParams,
          //     current: page,
          //     size: pageSize,
          //   });
          // },
        }}
        footer={() => (
          <Space>
            <Popconfirm
              title="Are you sure to delete this item?"
              onConfirm={deleteBatch}
            >
              <Button type="primary">Delete batch</Button>
            </Popconfirm>
            <Button type="primary" onClick={renewedBatch}>
              Renewed batch
            </Button>
          </Space>
        )}
      >
        <Button type="primary" onClick={() => router('add')}>
          Add pool license
        </Button>
      </CommonList>
    </>
  );
}

export default AdminPoolLicensesList;

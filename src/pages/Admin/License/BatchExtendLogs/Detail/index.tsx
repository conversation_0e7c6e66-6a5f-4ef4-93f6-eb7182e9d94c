import { Form, Input } from 'antd';
import { useQuery } from 'react-query';
import { useParams } from 'react-router-dom';
import { getLicenseBatchExtendLogsDetail } from '@/api/admin-license';
import useSetGlobalLoading from '@/hooks/useSetGlobalLoading';
import FormCom from '@/pages/Licenses/components/FormCom';

function BatchExtendLogsDetail() {
  const [form] = Form.useForm();
  const { id } = useParams();
  const { isLoading } = useQuery(
    ['LicenseBatchExtendLogsDetail', id],
    () => getLicenseBatchExtendLogsDetail(id || ''),
    {
      select: (res) => res.data,
      onSuccess: (data) => {
        form.setFieldsValue(data);
      },
    }
  );
  useSetGlobalLoading(isLoading);
  return (
    <FormCom pageTitle="Batch extend logs">
      <Form
        style={{ width: '600px' }}
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 14 }}
        disabled
        form={form}
      >
        <Form.Item label="Company" name="company">
          <Input />
        </Form.Item>
        <Form.Item label="Mode" name="mode">
          <Input />
        </Form.Item>
        <Form.Item label="Speed type" name="speedType">
          <Input />
        </Form.Item>
        <Form.Item label="Feature type" name="featureType">
          <Input />
        </Form.Item>
        <Form.Item label="New expiration date" name="newExpirationDate">
          <Input />
        </Form.Item>
        <Form.Item label="Old expiration date" name="oldExpirationDate">
          <Input />
        </Form.Item>
        <Form.Item label="Action date" name="actionDate">
          <Input />
        </Form.Item>
        <Form.Item label="Comment" name="comment">
          <Input />
        </Form.Item>
      </Form>
    </FormCom>
  );
}

export default BatchExtendLogsDetail;

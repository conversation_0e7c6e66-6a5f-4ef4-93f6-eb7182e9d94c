import { useState } from 'react';
import CommonList from '@/pages/Admin/components/CommonList';
import { useQuery } from 'react-query';
import { getLicenseReportingList } from '@/api/admin-license';
import { ColumnsType } from 'antd/es/table';
import { Link } from 'react-router-dom';
import useBreadcrumb from '@/hooks/useBreadcrumb';

type DataType = {
  id: number;
  name: string;
};

function LicenseReport() {
  const breadcrumb = useBreadcrumb();
  const [tableParams, setTableParams] = useState({
    current: 1,
    size: 10,
  });
  const { data } = useQuery(
    ['LicenseReport', tableParams],
    () => getLicenseReportingList(tableParams),
    {
      select: (res) => res.data,
    }
  );
  console.log(data);
  const columns: ColumnsType<DataType> = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => <Link to={`${record.id}`}>{text}</Link>,
    },
  ];
  return (
    <>
      <CommonList
        pageTitle="License reporting"
        dataSource={data?.records || []}
        columns={columns}
        showSearch={false}
        pagination={{
          size: 'small',
          total: data?.total || 0,
          showTotal: (total) => `Total ${total} items`,
          onChange: (page, pageSize) => {
            setTableParams({
              ...tableParams,
              current: page,
              size: pageSize,
            });
          },
        }}
      ></CommonList>
    </>
  );
}

export default LicenseReport;

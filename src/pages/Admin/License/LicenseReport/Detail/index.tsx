import { useState } from 'react';
import { Form, Select, DatePicker, Button, message } from 'antd';
import { useQuery } from 'react-query';
import { useParams } from 'react-router-dom';
import {
  getLicenseReportingDetail,
  getLicenseReportingDownload,
} from '@/api/admin-license';
import useSetGlobalLoading from '@/hooks/useSetGlobalLoading';
import FormCom from '@/pages/Licenses/components/FormCom';
import dayjs from 'dayjs';
import { downloadByBlob } from '@/utils/utils';

type FieldType = {
  company: string;
  startPeriod: string;
  endPeriod: string;
  actionType: string;
};

function LicenseReportDetail() {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const { id } = useParams();
  const { data: optionData, isLoading } = useQuery(
    ['LicenseReportDetail', id],
    () => getLicenseReportingDetail(id || ''),
    {
      select: (res) => res.data,
      onSuccess: (data) => {
        data.startPeriod = dayjs(data.startPeriod ?? new Date());
        data.endPeriod = dayjs(data.endPeriod ?? new Date());
        data.companyId = undefined;
        form.setFieldsValue(data);
      },
    }
  );
  useSetGlobalLoading(isLoading);

  const onFinish = async (value: any) => {
    setLoading(true);
    const params = {
      ...value,
      startPeriod: value.startPeriod?.format('YYYY-MM-DD'),
      endPeriod: value.endPeriod?.format('YYYY-MM-DD'),
    };
    const data = await getLicenseReportingDownload(params);
    setLoading(false);
    // 如果返回的是json，说明有错误
    if (data.data instanceof Blob && data.data.type === 'application/json') {
      // 将blob转成json
      const reader = new FileReader();
      reader.readAsText(data.data, 'utf-8');
      reader.onload = function () {
        const json = JSON.parse(reader.result as string);
        message.error(json.message);
      };
      return;
    }
    downloadByBlob(data);
  };
  return (
    <FormCom pageTitle="License reporting">
      <Form
        style={{ width: '600px' }}
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 14 }}
        form={form}
        onFinish={onFinish}
      >
        <Form.Item
          label="Company"
          name="companyId"
          rules={[{ required: true, message: 'Please select company' }]}
        >
          <Select
            showSearch
            options={optionData?.companyList || []}
            fieldNames={{ label: 'name', value: 'id' }}
            filterOption={(input, option) =>
              (option?.name ?? '').toLowerCase().includes(input.toLowerCase())
            }
          ></Select>
        </Form.Item>
        <Form.Item
          label="Start period"
          name="startPeriod"
          rules={[{ required: true, message: 'Please select start period' }]}
        >
          <DatePicker />
        </Form.Item>
        <Form.Item
          label="End period"
          name="endPeriod"
          dependencies={['startPeriod']}
          rules={[
            {
              required: true,
            },
            ({ getFieldValue }) => ({
              validator(_, value) {
                console.log(value);
                if (!value || getFieldValue('startPeriod') <= value) {
                  return Promise.resolve();
                }
                return Promise.reject(
                  new Error(
                    'The end period must be greater than the start period!'
                  )
                );
              },
            }),
          ]}
        >
          <DatePicker />
        </Form.Item>
        <Form.Item
          label="Action type"
          name="actionType"
          rules={[{ required: true, message: 'Please select action type' }]}
        >
          <Select options={optionData?.actionTypeList || []}></Select>
        </Form.Item>
        <Form.Item wrapperCol={{ offset: 8, span: 14 }}>
          <Button
            type="primary"
            htmlType="submit"
            style={{ marginTop: '10px' }}
            loading={loading}
          >
            Generate
          </Button>
        </Form.Item>
      </Form>
    </FormCom>
  );
}

export default LicenseReportDetail;

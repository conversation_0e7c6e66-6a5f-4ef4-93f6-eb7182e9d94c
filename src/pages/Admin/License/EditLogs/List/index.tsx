import { useState } from 'react';
import CommonList from '@/pages/Admin/components/CommonList';
import { useQuery } from 'react-query';
import { getLicenseEditLogs } from '@/api/admin-license';
import { ColumnsType } from 'antd/es/table';
import { Link } from 'react-router-dom';
import useBreadcrumb from '@/hooks/useBreadcrumb';

type DataType = {
  id: number;
  logs: string;
};

function EditLogsList() {
  const [tableParams, setTableParams] = useState({
    current: 1,
    size: 10,
    search: '',
  });
  const breadcrumb = useBreadcrumb();
  const { data } = useQuery(
    ['editLogsList', tableParams],
    () => getLicenseEditLogs(tableParams),
    {
      select: (res) => res.data,
    }
  );
  const columns: ColumnsType<DataType> = [
    {
      title: 'Logs',
      dataIndex: 'logs',
      key: 'logs',
      render: (text, record) => (
        <Link to={`${record.id}`} onClick={() => breadcrumb(record.logs)}>
          {text}
        </Link>
      ),
    },
  ];
  const handleSearch = (value: string) => {
    setTableParams({
      ...tableParams,
      current: 1,
      search: value,
    });
  };
  return (
    <>
      <CommonList
        pageTitle="License edit logs"
        dataSource={data?.records || []}
        columns={columns}
        showAddBtn={false}
        handleSearch={handleSearch}
        pagination={{
          size: 'small',
          total: data?.total || 0,
          current: tableParams.current,
          showTotal: (total) => `Total ${total} items`,
          onChange: (page, pageSize) => {
            setTableParams({
              ...tableParams,
              current: page,
              size: pageSize,
            });
          },
        }}
      ></CommonList>
    </>
  );
}

export default EditLogsList;

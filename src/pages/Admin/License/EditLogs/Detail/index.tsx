import { Form, Input } from 'antd';
import { useQuery } from 'react-query';
import { useParams } from 'react-router-dom';
import { getLicenseEditLogsDetail } from '@/api/admin-license';
import useSetGlobalLoading from '@/hooks/useSetGlobalLoading';
import FormCom from '@/pages/Licenses/components/FormCom';

function EditLogsDetail() {
  const [form] = Form.useForm();
  const { id } = useParams();
  const { isLoading } = useQuery(
    ['editLogsDetail', id],
    () => getLicenseEditLogsDetail(id || ''),
    {
      select: (res) => res.data,
      onSuccess: (data) => {
        form.setFieldsValue(data);
      },
    }
  );
  useSetGlobalLoading(isLoading);
  return (
    <FormCom pageTitle="License edit logs">
      <Form
        style={{ width: '600px' }}
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 14 }}
        disabled
        form={form}
      >
        <Form.Item label="Company" name="company">
          <Input />
        </Form.Item>
        <Form.Item label="Mode" name="mode">
          <Input />
        </Form.Item>
        <Form.Item label="Speed type" name="speedType">
          <Input />
        </Form.Item>
        <Form.Item label="Feature type" name="featureType">
          <Input />
        </Form.Item>
        <Form.Item label="Hardware id" name="oldHardwareId">
          <Input />
        </Form.Item>
        <Form.Item label="New hardware id" name="newHardwareId">
          <Input />
        </Form.Item>
        <Form.Item label="Expire date" name="oldExpireDate">
          <Input />
        </Form.Item>
        <Form.Item label="Old Expire date" name="newExpireDate">
          <Input />
        </Form.Item>
        <Form.Item label="License name" name="oldLicenseName">
          <Input />
        </Form.Item>
        <Form.Item label="New license name" name="newLicenseName">
          <Input />
        </Form.Item>
        <Form.Item label="Status" name="status">
          <Input />
        </Form.Item>
        <Form.Item label="Comment" name="oldComment">
          <Input />
        </Form.Item>
        <Form.Item label="New comment" name="newComment">
          <Input />
        </Form.Item>
        <Form.Item label="Change date" name="changeDate">
          <Input />
        </Form.Item>
      </Form>
    </FormCom>
  );
}

export default EditLogsDetail;

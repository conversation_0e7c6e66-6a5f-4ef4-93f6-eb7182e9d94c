import { Button, Form, Input, message } from 'antd';
import { useQuery } from 'react-query';
import { useNavigate, useParams } from 'react-router-dom';
import {
  getFeatureTypesById,
  postFeatureTypes,
  putFeatureTypes,
} from '@/api/admin-license';
import useSetGlobalLoading from '@/hooks/useSetGlobalLoading';
import FormCom from '@/pages/Licenses/components/FormCom';

function FeatureTypesDetail() {
  const router = useNavigate();
  const [form] = Form.useForm();
  const { id } = useParams();
  const { isLoading } = useQuery(
    ['FeatureTypesDetail', id],
    () => getFeatureTypesById(id || ''),
    {
      enabled: !!id,
      select: (res) => res.data,
      onSuccess: (data) => {
        form.setFieldsValue(data);
      },
    }
  );
  useSetGlobalLoading(isLoading);
  const onFinish = async (value: any) => {
    if (id) {
      const { data } = await putFeatureTypes({ ...value, id });
      console.log(data);
      if (data.result) {
        message.success('Updated successfully', 1).then(() => {
          router(-1);
        });
      } else {
        message.error(data.message, 1);
      }
    } else {
      const { data } = await postFeatureTypes({ ...value });
      if (data.result) {
        message.success('Created successfully', 1).then(() => {
          router(-1);
        });
      } else {
        message.error(data.message, 1);
      }
    }
  };
  return (
    <FormCom pageTitle="Feature types">
      <Form
        style={{ width: '600px' }}
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 14 }}
        form={form}
        onFinish={onFinish}
      >
        <Form.Item
          label="Name"
          name="name"
          rules={[
            {
              required: true,
              message: 'Please input your name!',
            },
          ]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          label="Code"
          name="type"
          rules={[
            {
              required: true,
              pattern: /^\d*(,\d*)*$/,
              message: 'incorrect format, must be 1,2,3',
            },
          ]}
        >
          <Input maxLength={10} />
        </Form.Item>
        <Form.Item wrapperCol={{ offset: 8, span: 14 }}>
          <Button type="primary" htmlType="submit">
            Save
          </Button>
        </Form.Item>
      </Form>
    </FormCom>
  );
}

export default FeatureTypesDetail;

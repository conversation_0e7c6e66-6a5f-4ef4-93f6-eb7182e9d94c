import { useState } from 'react';
import CommonList from '@/pages/Admin/components/CommonList';
import { useQuery } from 'react-query';
import {
  delFeatureTypeByIds,
  getFeatureTypesList,
  getFeatureTypesRelationList,
} from '@/api/admin-license';
import { ColumnsType } from 'antd/es/table';
import { Link, useNavigate } from 'react-router-dom';
import { Button, Modal, Spin, message } from 'antd';
import styles from '../../../Auth/Groups/ListPage/index.module.scss';
import usePermission from '@/hooks/usePermission';
import useBreadcrumb from '@/hooks/useBreadcrumb';

type DataType = {
  id: number;
  name: string;
};

function FeatureTypesList() {
  const { hasAdd, hasChange, hasDelete } = usePermission();
  const breadcrumb = useBreadcrumb();
  const router = useNavigate();
  const [tableParams, setTableParams] = useState({
    current: 1,
    size: 10,
  });
  const [idList, setIdList] = useState<React.Key[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { data, refetch } = useQuery(
    ['FeatureTypesList', tableParams],
    () => getFeatureTypesList(tableParams),
    {
      select: (res) => res.data,
    }
  );
  const { data: relationList, isLoading: relationLoading } = useQuery(
    ['featureTypeRelationList', isModalOpen],
    () => getFeatureTypesRelationList(idList),
    {
      select: (res) => res.data,
      enabled: isModalOpen,
    }
  );
  const columns: ColumnsType<DataType> = [
    {
      title: 'Feature type',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <Link to={`${record.id}`} onClick={() => breadcrumb(record.name)}>
          {text}
        </Link>
      ),
    },
    {
      title: 'Action',
      key: 'action',
      render: (text, record) =>
        hasDelete ? <a onClick={() => delItem(record)}>Delete</a> : '/',
    },
  ];

  const rowSelection = {
    selectedRowKeys: idList,
    onSelect: (record: any, selected: boolean) => {
      if (selected) {
        setIdList((before) => {
          return [...before, record.id];
        });
      } else {
        setIdList((before) => {
          return before.filter((el) => el !== record.id);
        });
      }
    },
    onSelectAll: (selected: boolean, selectedRows: any, changeRows: any) => {
      if (selected) {
        setIdList((before) => {
          const ids = changeRows.map((el: any) => el.id);
          return [...before, ...ids];
        });
      } else {
        const ids = changeRows.map((el: any) => el.id);
        setIdList((before) => {
          return before.filter((el) => ids.indexOf(el) == -1);
        });
      }
    },
  };
  const delAllFeature = () => {
    if (idList.length > 0) {
      setIsModalOpen(true);
    } else {
      message.warning('Please select at least one item!', 2);
    }
  };
  const delItem = (record: any) => {
    setIdList([record.id]);
    setIsModalOpen(true);
  };

  const handleDelSelect = async () => {
    if (relationLoading) return;
    const data = await delFeatureTypeByIds(idList);
    if (data) {
      message.success('Delete Success!', 2);
      setIsModalOpen(false);
      refetch();
      setIdList([]);
    }
  };
  return (
    <>
      <CommonList
        pageTitle="Feature types"
        dataSource={data?.records || []}
        columns={columns}
        showSearch={false}
        rowSelection={
          hasDelete
            ? {
                ...rowSelection,
              }
            : undefined
        }
        handleDelSelect={delAllFeature}
        pagination={{
          size: 'small',
          total: data?.total || 0,
          showTotal: (total) => `Total ${total} items`,
          onChange: (page, pageSize) => {
            setTableParams({
              ...tableParams,
              current: page,
              size: pageSize,
            });
          },
        }}
      >
        {hasAdd && (
          <Button type="primary" onClick={() => router('add')}>
            Add feature Type
          </Button>
        )}
      </CommonList>
      <Modal
        title="Delete Feature Typs"
        open={isModalOpen}
        onCancel={() => setIsModalOpen(false)}
        onOk={handleDelSelect}
        style={{ paddingBottom: '16px' }}
        destroyOnClose
      >
        <div className={styles.del_modal}>
          {relationLoading ? (
            <Spin size="large" />
          ) : (
            <>
              <p>
                Are you sure you want to delete the selected feature type? All
                of the following objects and their related items will be
                deleted:
              </p>
              <div className={styles.content_wrap}>
                {relationList &&
                  relationList.map((item: any, index: number) => (
                    <section key={index}>
                      <div>
                        {item.tableName}:{' '}
                        <Link to={item.url}>{item.content}</Link>
                      </div>
                      <ul>
                        {item.related.map((child: any, index: number) => {
                          return (
                            <li key={index}>
                              {child.tableName} :{' '}
                              {child.url.length > 0 ? (
                                <Link to={child.url}>{child.content}</Link>
                              ) : (
                                child.content
                              )}
                            </li>
                          );
                        })}
                      </ul>
                    </section>
                  ))}
              </div>
            </>
          )}
        </div>
      </Modal>
    </>
  );
}

export default FeatureTypesList;

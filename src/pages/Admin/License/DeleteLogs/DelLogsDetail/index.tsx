import { Form, Input } from 'antd';
import { useQuery } from 'react-query';
import { useParams } from 'react-router-dom';
import { getLicenseDelLogsDetail } from '@/api/admin-license';
import useSetGlobalLoading from '@/hooks/useSetGlobalLoading';
import FormCom from '@/pages/Licenses/components/FormCom';

function DelLogsDetail() {
  const [form] = Form.useForm();
  const { id } = useParams();
  const { isLoading } = useQuery(
    ['delLogsDetail', id],
    () => getLicenseDelLogsDetail(id || ''),
    {
      select: (res) => res.data,
      onSuccess: (data) => {
        form.setFieldsValue(data);
      },
    }
  );
  useSetGlobalLoading(isLoading);
  return (
    <FormCom pageTitle="License delete logs">
      <Form
        style={{ width: '600px' }}
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 14 }}
        disabled
        form={form}
      >
        <Form.Item label="Company" name="company">
          <Input />
        </Form.Item>
        <Form.Item label="Mode" name="mode">
          <Input />
        </Form.Item>
        <Form.Item label="Speed type" name="speedType">
          <Input />
        </Form.Item>
        <Form.Item label="Feature type" name="featureType">
          <Input />
        </Form.Item>
        <Form.Item label="Hardware id" name="hardwareId">
          <Input />
        </Form.Item>
        <Form.Item label="Expire date" name="expireDate">
          <Input />
        </Form.Item>
        <Form.Item label="Status" name="status">
          <Input />
        </Form.Item>
        <Form.Item label="Delete date" name="deleteDate">
          <Input />
        </Form.Item>
      </Form>
    </FormCom>
  );
}

export default DelLogsDetail;

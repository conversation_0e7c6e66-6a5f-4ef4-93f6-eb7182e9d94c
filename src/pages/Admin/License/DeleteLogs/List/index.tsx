import { getLicenseDelLogs } from '@/api/admin-license';
import useBreadcrumb from '@/hooks/useBreadcrumb';
import CommonList from '@/pages/Admin/components/CommonList';
import { ColumnsType } from 'antd/es/table';
import { useState } from 'react';
import { useQuery } from 'react-query';
import { Link } from 'react-router-dom';

type DataType = {
  id: number;
  logs: string;
};

function DeleteLogsList() {
  const [current, setCurrent] = useState(1);
  const breadcrumb = useBreadcrumb();
  const { data } = useQuery(
    ['deleteLogsList', current],
    () => getLicenseDelLogs(current),
    {
      select: (res) => res.data,
    }
  );
  const columns: ColumnsType<DataType> = [
    {
      title: 'Logs',
      dataIndex: 'logs',
      key: 'logs',
      render: (text, record) => (
        <Link to={`${record.id}`} onClick={() => breadcrumb(record.logs)}>
          {text}
        </Link>
      ),
    },
  ];
  const onPaginationChange = (page: number, pageSize?: number) => {
    setCurrent(page);
  };
  return (
    <>
      <CommonList
        pageTitle="License delete logs"
        dataSource={data?.records || []}
        columns={columns}
        showAddBtn={false}
        showSearch={false}
        pagination={{
          size: 'small',
          total: data?.total || 0,
          showTotal: (total) => `Total ${total} items`,
          showSizeChanger: false,
          onChange: onPaginationChange,
        }}
      ></CommonList>
    </>
  );
}

export default DeleteLogsList;

import { Form, Input } from 'antd';
import { useQuery } from 'react-query';
import { useParams } from 'react-router-dom';
import { getLicenseSummaryLogsDetail } from '@/api/admin-license';
import useSetGlobalLoading from '@/hooks/useSetGlobalLoading';
import FormCom from '@/pages/Licenses/components/FormCom';

function SummaryLogsDetail() {
  const [form] = Form.useForm();
  const { id } = useParams();
  const { isLoading } = useQuery(
    ['summaryLogsDetail', id],
    () => getLicenseSummaryLogsDetail(id || ''),
    {
      select: (res) => res.data,
      onSuccess: (data) => {
        form.setFieldsValue(data);
      },
    }
  );
  useSetGlobalLoading(isLoading);
  return (
    <FormCom pageTitle="License summary logs">
      <Form
        style={{ width: '600px' }}
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 14 }}
        disabled
        form={form}
      >
        <Form.Item label="Company" name="company">
          <Input />
        </Form.Item>
        <Form.Item label="Mode" name="mode">
          <Input />
        </Form.Item>
        <Form.Item label="Speed type" name="speedType">
          <Input />
        </Form.Item>
        <Form.Item label="Feature type" name="featureType">
          <Input />
        </Form.Item>
        <Form.Item label="Old license limit" name="oldAvailableValue">
          <Input />
        </Form.Item>
        <Form.Item label="New license limit" name="newAvailableValue">
          <Input />
        </Form.Item>
        <Form.Item label="Expiration date" name="expirationDate">
          <Input />
        </Form.Item>
        <Form.Item label="Comment" name="comment">
          <Input />
        </Form.Item>
        <Form.Item label="Changed date" name="changed">
          <Input />
        </Form.Item>
      </Form>
    </FormCom>
  );
}

export default SummaryLogsDetail;

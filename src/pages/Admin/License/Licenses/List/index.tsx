import React, { useRef, useState } from 'react';
import CommonList from '@/pages/Admin/components/CommonList';
import { useQuery } from 'react-query';
import {
  delAdminLicenseByIds,
  getAdminLicenseList,
  getCompanyList,
  putAdminLicenseDisableByIds,
  putAdminLicenseEnableByIds,
} from '@/api/admin-license';
import { ColumnsType } from 'antd/es/table';
import { Link, useNavigate } from 'react-router-dom';
import { Button, Space, message, Popconfirm, Select } from 'antd';
import { useAppSelector } from '@/store/hooks';
import { selectUserInfo } from '@/store/feature/userSlice';
import usePermission from '@/hooks/usePermission';
import useBreadcrumb from '@/hooks/useBreadcrumb';
import ExtraHeader from '../components/ExtraHeader';

type DataType = {
  companyName: string;
  createTime: string;
  decommissioned: number;
  expirationDate: string;
  featureType: string;
  hardwareId: string;
  id: number;
  mode: string;
  speedType: string;
  status: string;
};
type TableParams = {
  current: number;
  size: number;
  search: string;
  sortList: { column: string; order: string }[];
  companyId?: number;
};
function AdminLicensesList() {
  const { hasAdd } = usePermission();
  const breadcrumb = useBreadcrumb();
  const router = useNavigate();
  const userInfo = useAppSelector(selectUserInfo);
  const [tableParams, setTableParams] = useState<TableParams>({
    current: 1,
    size: 10,
    search: '',
    sortList: [],
    companyId: undefined,
  });
  // const selectedKeys = useRef<React.Key[]>([]);
  const [selectedKeys, setSelectedKeys] = useState<{
    [key: number]: React.Key[];
  }>({ [tableParams.current]: [] });
  const { data, refetch } = useQuery(
    ['adminLicensesList', tableParams],
    () => getAdminLicenseList(tableParams),
    {
      select: (res) => res.data,
    }
  );
  const { data: companyList } = useQuery(
    'companyList',
    () => getCompanyList(),
    {
      select: (res) => res.data,
    }
  );

  // console.log(companyList);
  const columns: ColumnsType<DataType> = [
    {
      title: 'Hardware ID',
      dataIndex: 'hardwareId',
      render: (text, record) => (
        <Link
          to={`${record.id}`}
          onClick={() =>
            breadcrumb(
              `${record.companyName}-${record.mode}-${record.speedType}-${record.featureType}(${record.createTime})`
            )
          }
        >
          {text ? text : '(None)'}
        </Link>
      ),
      sorter: {
        multiple: 1,
      },
      width: 140,
      fixed: 'left',
    },
    {
      title: 'Create time',
      dataIndex: 'createTime',
      sorter: {
        multiple: 2,
      },
    },
    {
      title: 'Expire date',
      dataIndex: 'expirationDate',
      sorter: {
        multiple: 3,
      },
    },
    {
      title: 'Company',
      dataIndex: 'companyName',
      sorter: {
        multiple: 4,
      },
    },
    {
      title: 'Status',
      dataIndex: 'status',
      sorter: {
        multiple: 5,
      },
    },
    {
      title: 'Speed type',
      dataIndex: 'speedType',
      sorter: {
        multiple: 6,
      },
    },
    {
      title: 'Feature type',
      dataIndex: 'featureType',
      sorter: {
        multiple: 7,
      },
    },
    {
      title: 'Serial Number',
      dataIndex: 'serialNumber',
    },
    {
      title: 'Organizational Information',
      dataIndex: 'organizationalInformation',
    },
    {
      title: 'FS Order Number',
      dataIndex: 'fsOrderNumber',
    },
    {
      title: 'Purchase Order Number',
      dataIndex: 'purchaseOrderNumber',
    },
    {
      title: 'Production Order Number',
      dataIndex: 'orderNumber',
    },
    {
      title: 'Mode',
      dataIndex: 'mode',
      sorter: {
        multiple: 8,
      },
    },
    {
      title: 'Action',
      key: 'action',
      width: 180,
      render: (text, record) =>
        userInfo.userType === 2 && (
          <Space>
            <Popconfirm
              title="Are you sure to delete this item?"
              onConfirm={() => delItem(record.id)}
            >
              <a>Delete</a>
            </Popconfirm>
            <a onClick={() => disableItem(record.id)}>Disable</a>
            <a onClick={() => enableItem(record.id)}>Enable</a>
          </Space>
        ),
    },
  ];
  const rowSelection = {
    onChange: (selectedRowKeys: React.Key[]) => {
      const temp = { ...selectedKeys };
      temp[tableParams.current] = selectedRowKeys;
      setSelectedKeys(temp);
    },
    selectedRowKeys: selectedKeys[tableParams.current],
  };
  const delItem = (id: number) => {
    fetchDelSelect([id]);
  };
  const disableItem = (id: number) => {
    fetchDisableSelect([id]);
  };
  const enableItem = (id: number) => {
    fetchEnableSelect([id]);
  };
  const fetchDelSelect = async (ids: React.Key[]) => {
    const data = await delAdminLicenseByIds(ids);
    if (data) {
      message.success('Delete Success!', 2);
      refetch();
      setSelectedKeys({ [tableParams.current]: [] });
    }
  };
  const fetchDisableSelect = async (ids: React.Key[]) => {
    const data = await putAdminLicenseDisableByIds(ids);
    if (data) {
      message.success('Disable Success!', 2);
      refetch();
      setSelectedKeys({ [tableParams.current]: [] });
    }
  };
  const fetchEnableSelect = async (ids: React.Key[]) => {
    const data = await putAdminLicenseEnableByIds(ids);
    if (data) {
      message.success('Enable Success!', 2);
      refetch();
      setSelectedKeys({ [tableParams.current]: [] });
    }
  };
  const handleSearch = (value: string) => {
    setTableParams({
      ...tableParams,
      current: 1,
      search: value,
    });
    setSelectedKeys({});
  };
  const deleteBatch = () => {
    // 判断selectedKeys每个对象的值是否都是空数组
    //如果不是空数组，将selectedKeys的值合并成一个数组
    const idOrIds = Object.values(selectedKeys).reduce(
      (pre, cur) => [...pre, ...cur],
      []
    );
    if (idOrIds.length > 0) {
      fetchDelSelect(idOrIds);
    } else {
      message.warning('Please select at least one item!', 2);
    }
  };
  const disableBatch = () => {
    // if (Object.values(selectedKeys).length > 0) {
    //   fetchDisableSelect();
    // } else {
    //   message.warning('Please select at least one item!', 2);
    // }
    const idOrIds = Object.values(selectedKeys).reduce(
      (pre, cur) => [...pre, ...cur],
      []
    );
    if (idOrIds.length > 0) {
      fetchDisableSelect(idOrIds);
    } else {
      message.warning('Please select at least one item!', 2);
    }
  };
  const enableBatch = () => {
    const idOrIds = Object.values(selectedKeys).reduce(
      (pre, cur) => [...pre, ...cur],
      []
    );
    if (idOrIds.length > 0) {
      fetchEnableSelect(idOrIds);
    } else {
      message.warning('Please select at least one item!', 2);
    }
  };
  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    const sortList = [];
    if (sorter instanceof Array) {
      sorter.forEach((item) => {
        sortList.push({
          column: item.field,
          order: item.order === 'ascend' ? 'ASC' : 'DESC',
        });
      });
    } else {
      if (sorter.order) {
        sortList.push({
          column: sorter.field,
          order: sorter.order === 'ascend' ? 'ASC' : 'DESC',
        });
      }
    }
    setTableParams({
      ...tableParams,
      current: pagination.current,
      size: pagination.pageSize,
      sortList,
    });
  };
  const handleCompanyChange = (value: number) => {
    setTableParams({
      ...tableParams,
      current: 1,
      companyId: value,
    });
  };
  return (
    <>
      <CommonList
        pageTitle="Admin Licenses"
        dataSource={data?.records || []}
        columns={columns}
        handleSearch={handleSearch}
        rowSelection={{
          ...rowSelection,
        }}
        scroll={{ x: 1300 }}
        onChange={handleTableChange}
        pagination={{
          size: 'small',
          total: data?.total || 0,
          current: tableParams.current,
          showTotal: (total) => `Total ${total} items`,
          // onChange: (page, pageSize) => {
          //   setTableParams({
          //     ...tableParams,
          //     current: page,
          //     size: pageSize,
          //   });
          // },
        }}
        footer={() =>
          userInfo.userType === 2 && (
            <Space>
              <Popconfirm
                title="Are you sure to delete this item?"
                onConfirm={deleteBatch}
              >
                <Button type="primary">Delete batch</Button>
              </Popconfirm>
              <Button type="primary" onClick={disableBatch}>
                Disable batch
              </Button>
              <Button type="primary" onClick={enableBatch}>
                Enable batch
              </Button>
            </Space>
          )
        }
        extra={
          <ExtraHeader
            companyList={companyList}
            handleCompanyChange={handleCompanyChange}
          />
        }
      >
        {hasAdd && (
          <Button type="primary" onClick={() => router('add')}>
            Add license
          </Button>
        )}
      </CommonList>
    </>
  );
}

export default AdminLicensesList;

import React, { useState } from 'react';
import {
  Select,
  Button,
  Space,
  Upload,
  UploadProps,
  message,
  Modal,
  Form,
  DatePicker,
} from 'antd';
import {
  getExcelDownloadFilter,
  postCompanyExcelDownload,
  postCompanyExcelUpload,
} from '@/api/admin-license';
import { CompanyExcelDownloadParams } from '@/api/interface';
import { downloadByBlob } from '@/utils/utils';
import { useQuery } from 'react-query';

interface ExtraProps {
  companyList: any;
  handleCompanyChange: (value: any) => void;
  downloadParams?: CompanyExcelDownloadParams;
}

function ExtraHeader(props: ExtraProps) {
  const { companyList, handleCompanyChange, downloadParams } = props;
  const [spinning, setSpinning] = useState(false);
  const [downloadModalVisible, setDownloadModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [btnLoading, setBtnLoading] = useState(false);

  const { data: filterOptions } = useQuery(
    'excelDownloadFilter',
    () => getExcelDownloadFilter(),
    {
      enabled: downloadModalVisible,
      select: (res) => res.data,
    }
  );
  console.log(filterOptions);
  const handleDownloadExcel = async () => {
    setDownloadModalVisible(true);
  };
  // 上传文件配置
  const uploadProps: UploadProps = {
    showUploadList: false,
    accept: '.xlsx, .xls',
    maxCount: 1,
    multiple: false,
    beforeUpload: (file) => {
      if (file.size > 1024 * 1024 * 2) {
        message.error('File must be smaller than 2MB!');
        return false;
      }
    },
    customRequest: async ({ file }) => {
      setSpinning(true);
      const formData = new FormData();
      formData.append('file', file);
      const res = await postCompanyExcelUpload(formData).finally(() =>
        setSpinning(false)
      );
      console.log(res);
      if (!res.data.isSuccess) {
        if (res.data.errorList.length) {
          Modal.confirm({
            title: 'Error',
            content: (
              <div style={{ height: 520, overflow: 'auto' }}>
                {res.data.errorList?.map((item: any, index: number) => (
                  <div key={index}>
                    {item.errorMsg?.map((msg: string, i: number) => (
                      <p key={i}>{msg}</p>
                    ))}
                  </div>
                ))}
              </div>
            ),
          });
        } else {
          message.error(res.data.message);
        }
      } else {
        message.success('Upload successfully');
      }
    },
  };
  const formSubmit = async () => {
    const formData = form.getFieldsValue();
    const params = {} as any;
    Object.entries(formData).forEach(([key, value]) => {
      if (value) {
        if (key === 'expireDate' || key === 'createDate') {
          if (value instanceof Array && value.length) {
            params[key] = value.map((item: any) => item.format('YYYY-MM-DD'));
          }
        } else {
          params[key] = value;
        }
      }
    });
    setBtnLoading(true);
    const data = await postCompanyExcelDownload(params).finally(() =>
      setBtnLoading(false)
    );
    downloadByBlob(data);
  };
  return (
    <>
      <Space size={16}>
        <Upload {...uploadProps}>
          <Button type="primary" loading={spinning}>
            Upload
          </Button>
        </Upload>
        <Button type="primary" onClick={handleDownloadExcel}>
          Download
        </Button>
        {/* <Select
        placeholder="Company"
        style={{ width: 140 }}
        showSearch
        allowClear
        filterOption={(input: string, option?: { children: string }) =>
          (option?.children ?? '').toLowerCase().includes(input.toLowerCase())
        }
        onChange={handleCompanyChange}
      >
        {companyList?.map((item: any) => (
          <Select.Option key={item.id} value={item.id}>
            {item.name}
          </Select.Option>
        ))}
      </Select> */}
      </Space>
      <Modal
        title="Download Licenses"
        open={downloadModalVisible}
        maskClosable={false}
        onCancel={() => setDownloadModalVisible(false)}
        okText="Download"
        centered
        onOk={formSubmit}
        confirmLoading={btnLoading}
      >
        <div>
          <p>
            Please select the data you wish to download. If none are selected,
            all data will be downloaded by default.
          </p>
          <Form form={form}>
            <Form.Item label="Company" name={'companyId'}>
              <Select
                placeholder="Please select"
                showSearch
                allowClear
                filterOption={(input: string, option?: { children: string }) =>
                  (option?.children ?? '')
                    .toLowerCase()
                    .includes(input.toLowerCase())
                }
              >
                {filterOptions?.companyList?.map((item: any) => (
                  <Select.Option key={item.id} value={item.id}>
                    {item.name}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
            <Form.Item label="Speed Type" name={'speedTypeId'}>
              <Select placeholder="Please select">
                {filterOptions?.speedTypeList?.map((item: any) => (
                  <Select.Option key={item.id}>{item.name}</Select.Option>
                ))}
              </Select>
            </Form.Item>
            <Form.Item label="Expire Date" name={'expireDate'}>
              <DatePicker.RangePicker />
            </Form.Item>
            <Form.Item label="Create Date" name={'createDate'}>
              <DatePicker.RangePicker />
            </Form.Item>
          </Form>
        </div>
      </Modal>
    </>
  );
}

export default ExtraHeader;

import { Button, Result } from 'antd';
import { useNavigate } from 'react-router-dom';
import '../404/404.scss';

function Unauthorized() {
  const router = useNavigate();
  return (
    <Result
      status="403"
      title="403"
      subTitle="Sorry, you are not authorized to access this page."
      extra={
        <Button type="primary" onClick={() => router('/')}>
          Back Home
        </Button>
      }
      rootClassName="not_found"
    />
  );
}

export default Unauthorized;

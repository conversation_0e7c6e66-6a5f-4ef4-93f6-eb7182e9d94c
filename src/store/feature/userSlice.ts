import { createSlice } from '@reduxjs/toolkit';
import { RootState } from '..';
import { AdminMenuType, CompanyCountType, Role } from '@/api/interface';

// 定义 state 的类型
export interface UserState {
  token: string;
  companyCount: CompanyCountType;
  userInfo: {
    userName: string;
    userType: Role;
    isScatteredCustomer: boolean;
  };
  permission: AdminMenuType[];
}
const initialState: UserState = {
  token: '',
  companyCount: {
    companyName: '',
    freeLicenseCount: 0,
    freeAnmConLicenseCount: 0,
  },
  userInfo: {
    userName: '',
    userType: Role.NONE,
    isScatteredCustomer: false,
  },
  permission: [],
};

// 创建一个 Slice
export const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    setToken: (state, action) => {
      state.token = action.payload;
      localStorage.setItem('token', action.payload);
    },
    setCompanyCount: (state, action) => {
      state.companyCount = action.payload;
    },
    setUserInfo: (state, action) => {
      state.userInfo = action.payload;
    },
    setPermission: (state, action) => {
      const data = action.payload as AdminMenuType[];
      data?.forEach((item) => {
        item.menuList.forEach((child) => {
          const hasAdd = child.rightList.some((item) =>
            item.label.includes('add')
          );
          const hasChange = child.rightList.some((item) =>
            item.label.includes('change')
          );
          const hasDelete = child.rightList.some((item) =>
            item.label.includes('delete')
          );
          child.permission = {
            hasAdd,
            hasChange,
            hasDelete,
          };
        });
      });
      state.permission = data;
    },
  },
});

export const { setToken, setCompanyCount, setUserInfo, setPermission } =
  userSlice.actions;
export const selectToken = (state: RootState) => state.user.token;
export const selectCompanyCount = (state: RootState) => state.user.companyCount;
export const selectUserInfo = (state: RootState) => state.user.userInfo;
export const selectPermission = (state: RootState) => state.user.permission;
export default userSlice.reducer;

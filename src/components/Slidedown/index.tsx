import React, { useState, useEffect, useRef } from 'react';
import styles from './styles.module.css';
import classNames from 'classnames/bind';

const cx = classNames.bind(styles);

interface SlideDownProps {
  children: React.ReactNode;
  isOpen: boolean;
}

const SlideDown = ({ children, isOpen }: SlideDownProps) => {
  const [shouldRender, setShouldRender] = useState(isOpen);
  const contentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (isOpen) {
      setShouldRender(true);
    }
  }, [isOpen]);

  const onAnimationEnd = () => {
    if (!isOpen) {
      setShouldRender(false);
    }
  };

  return (
    shouldRender && (
      <div
        className={cx(['slide-down', isOpen ? 'open' : 'close'])}
        onAnimationEnd={onAnimationEnd}
        ref={contentRef}
      >
        {children}
      </div>
    )
  );
};

export default SlideDown;

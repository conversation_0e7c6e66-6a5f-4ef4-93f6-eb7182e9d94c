import { Input, InputProps } from 'antd';
// type ThemeData = {
//   borderRadius: number;
//   colorPrimary: string;
//   Button?: {
//     colorPrimary: string;
//     algorithm?: boolean;
//   };
// };

// const defaultData: ThemeData = {
//   borderRadius: 6,
//   colorPrimary: '#ef5a28',
//   Button: {
//     colorPrimary: '#ef5a28',
//   },
// };

function PcInput(props: InputProps) {
  return <Input {...props} />;
}

export default PcInput;

import { ColumnsType, TableProps } from 'antd/es/table';
import { ConfigProvider, Table, theme } from 'antd';
import './table.scss';

export interface DefaultData {
  id: number;
}
export interface CustomTableProps<T> extends TableProps<T> {
  columns: ColumnsType<T>;
  dataSource: T[];
  index?: number;
  customizeRenderEmpty?: () => React.ReactNode;
}
function PcTable<T extends DefaultData>(props: CustomTableProps<T>) {
  const {
    columns,
    dataSource,
    pagination,
    expandable,
    index,
    customizeRenderEmpty,
    ...reset
  } = props;
  return (
    <ConfigProvider
      renderEmpty={customizeRenderEmpty}
      theme={{
        algorithm: [theme.defaultAlgorithm],
        components: {
          Table: {
            cellFontSizeSM: 12,
            headerBorderRadius: index === 0 ? 8 : 0,
          },
        },
      }}
    >
      <Table<T>
        rowKey={(record) => record.id}
        columns={columns}
        dataSource={dataSource}
        size="small"
        pagination={
          pagination && {
            rootClassName: 'pica_pagination',
            ...pagination,
          }
        }
        expandable={expandable}
        {...reset}
      ></Table>
    </ConfigProvider>
  );
}

export default PcTable;

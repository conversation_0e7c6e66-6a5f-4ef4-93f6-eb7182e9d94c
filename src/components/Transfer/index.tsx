import React, { Key, useState } from 'react';
import { ConfigProvider, Transfer, TransferProps } from 'antd';
import type { TransferDirection } from 'antd/es/transfer';

interface RecordType {
  key: string;
  title: string;
  description?: string;
}

interface TransferProp extends TransferProps<RecordType> {
  dataSource?: RecordType[];
  value?: string[];
  onChange?: (targetKeys: Key[], direction?: TransferDirection) => void;
}

function PcTransfer(props: TransferProp) {
  const { dataSource, value, onChange } = props;
  const [targetKeys, setTargetKeys] = useState<Key[]>([]);
  const [selectedKeys, setSelectedKeys] = useState<Key[]>([]);

  const handleChange = (
    nextTargetKeys: Key[],
    direction: TransferDirection,
    moveKeys: Key[]
  ) => {
    console.log('targetKeys:', nextTargetKeys);
    console.log('direction:', direction);
    console.log('moveKeys:', moveKeys);
    setTargetKeys(nextTargetKeys);
    onChange?.(nextTargetKeys);
  };

  const onSelectChange = (
    sourceSelectedKeys: Key[],
    targetSelectedKeys: Key[]
  ) => {
    console.log('sourceSelectedKeys:', sourceSelectedKeys);
    console.log('targetSelectedKeys:', targetSelectedKeys);
    setSelectedKeys([...sourceSelectedKeys, ...targetSelectedKeys]);
  };

  // const onScroll = (
  //   direction: TransferDirection,
  //   e: React.SyntheticEvent<HTMLUListElement>
  // ) => {
  //   console.log('direction:', direction);
  //   console.log('target:', e.target);
  // };

  return (
    <ConfigProvider
      theme={{
        components: {
          Transfer: {
            listWidth: 300,
            listHeight: 300,
          },
        },
        // algorithm: [theme.defaultAlgorithm],
      }}
    >
      <Transfer
        showSearch
        dataSource={dataSource}
        titles={['Available permissions', 'Chosen permissions']}
        targetKeys={value || targetKeys}
        selectedKeys={selectedKeys}
        onChange={handleChange}
        onSelectChange={onSelectChange}
        // onScroll={onScroll}
        render={(item) => item.title}
      />
    </ConfigProvider>
  );
}

export default PcTransfer;

user  nginx;
worker_processes  auto;

error_log  /var/log/nginx/error.log warn;
pid        /var/run/nginx.pid;

events {
    worker_connections  1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    sendfile        on;
    #tcp_nopush     on;

    keepalive_timeout  65;

    gzip on;
    gzip_min_length 1k;
    gzip_buffers 4 8k;
    gzip_http_version 1.1;
    gzip_comp_level 6;
    gzip_types text/plain qpplication/x-javascript text/css application/xml text/javascript application/javascript application/json;
    gzip_disable "MSIE [1-6]";
    gzip_vary on;
    server {
        listen 80 default_server;
        server_name _;

        location  / {
            root /usr/share/nginx/html;
            index  index.html ;
            add_header Access-Control-Allow-Origin *;
            try_files $uri $uri/ /index.html;
        }

    }

}


import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react-swc';
import path from 'path';
import eslintPlugin from 'vite-plugin-eslint';

import { sentryVitePlugin } from '@sentry/vite-plugin';

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd());
  return {
    base: env.VITE_CDN_URL,
    plugins: [
      react(),
      eslintPlugin(),
      sentryVitePlugin({
        url: 'https://sentry.fs.com/',
        org: 'sentry',
        project: 'pica8-license',
        authToken:
          '2d24fd44a82c2167a92b3cd82fa28a11f79207469685ead13473a59f1d684c0c',
      }),
    ],
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src'), // src 路径
      },
    },
    //打包配置，切割代码块，让其内容更小
    build: {
      rollupOptions: {
        output: {
          manualChunks(id) {
            if (id.includes('node_modules') && !id.includes('sentry')) {
              return id
                .toString()
                .split('node_modules/')[1]
                .split('/')[0]
                .toString();
            }
          },
        },
      },
      sourcemap: true,
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: '@import "@/styles/variables.scss";',
        },
      },
    },
    server: {
      port: 3067,
      host: '0.0.0.0',
    },
    esbuild: {
      drop: mode === 'production' ? ['console', 'debugger'] : [],
    },
  };
});

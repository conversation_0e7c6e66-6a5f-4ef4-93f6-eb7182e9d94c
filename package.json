{"name": "pica8-front", "private": true, "version": "0.0.0", "scripts": {"dev": "vite", "dev:prod": "vite --mode production", "build:test": "tsc && vite build --mode test", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint --ext .ts --ext .tsx src --fix", "prepare": "npx husky install"}, "dependencies": {"@ant-design/icons": "^5.0.1", "@fs-front/monitor": "^1.0.7", "@fs-front/sso-login": "^1.0.1", "@reduxjs/toolkit": "^1.9.5", "@sentry/react": "^7.107.0", "@types/js-cookie": "^3.0.6", "antd": "^5.18.3", "axios": "^1.4.0", "classnames": "^2.3.2", "dayjs": "^1.11.7", "js-cookie": "^3.0.5", "qs": "^6.12.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-query": "^3.39.3", "react-redux": "^8.0.5", "react-router-dom": "^6.11.1", "redux-persist": "^6.0.0"}, "devDependencies": {"@commitlint/cli": "^17.6.3", "@commitlint/config-conventional": "^17.6.3", "@sentry/vite-plugin": "^2.16.0", "@types/qs": "^6.9.15", "@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@typescript-eslint/eslint-plugin": "^5.59.1", "@typescript-eslint/parser": "^5.59.1", "@vitejs/plugin-react-swc": "^3.0.0", "eslint": "^8.39.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "husky": "^8.0.3", "lint-staged": "^13.2.2", "prettier": "^2.8.8", "sass": "^1.62.1", "typescript": "^4.9.3", "vite": "^4.2.1", "vite-plugin-eslint": "^1.8.1"}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx,json,md}": ["eslint --fix --max-warnings=0", "prettier --write"], "src/**/*.{scss,less,css}": ["prettier --write"]}}